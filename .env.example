# Configure these with your own Docker registry images
DOCKER_IMAGE_BACKEND=backend
DOCKER_IMAGE_FRONTEND=frontend

FRONTEND_HOST=http://localhost:3000
BACKEND_HOST=http://localhost:8000

OBSERVABILITY=true

# Environment: local, staging, production
ENVIRONMENT=local

PROJECT_NAME="YourAI"
STACK_NAME=your-ai

# Google
GOOGLE_CLIENT_ID=
GOOGLE_CLIENT_SECRET=

# Backend
BACKEND_CORS_ORIGINS="http://localhost:3000"
SECRET_KEY=Ph7eeesM.m7WteT4Cy-M
FIRST_SUPERUSER=<EMAIL>
FIRST_SUPERUSER_PASSWORD=Ph7eeesM.m7WteT4Cy-M

# Flower dashboard credentials (for Celery monitoring)
FLOWER_USER=admin
FLOWER_PASSWORD=Ph7eeesM.m7WteT4Cy-M

# Redis Cache Settings
# Used for session management and caching
REDIS_SERVER=redis
REDIS_PORT=6379
REDIS_CELERY_DB=0

# Emails
SMTP_HOST=
SMTP_USER=
SMTP_PASSWORD=
EMAILS_FROM_EMAIL=<EMAIL>
SMTP_TLS=True
SMTP_SSL=False
SMTP_PORT=587

# Postgres
POSTGRES_SERVER=localhost
POSTGRES_PORT=5432
POSTGRES_DB=uni-studio
POSTGRES_USER=postgres
POSTGRES_PASSWORD=Ph7eeesM.m7WteT4Cy-M

SENTRY_DSN=

# ----------------------------------------------------------
# -------------- Object Storage Configuration --------------
# ----------------------------------------------------------
# Choose which storage option to use: "s3" or "minio"
OBJECT_STORAGE_OPTION=minio
IMAGES_BUCKET=
KB_BUCKET=

# S3 Configuration (used when OBJECT_STORAGE_OPTION=s3)
S3_ACCESS_KEY=
S3_SECRET_KEY=
S3_REGION='ap-southeast-1'

# MinIO Configuration (used when OBJECT_STORAGE_OPTION=minio)
# For Docker setup (services connecting to MinIO inside Docker network)
MINIO_ENDPOINT=minio:9000
# For local development outside Docker (services connecting to MinIO on localhost)
# MINIO_ENDPOINT=localhost:9000
MINIO_ROOT_USER=admin
MINIO_ROOT_PASSWORD=Ph7eeesM.m7WteT4Cy-M
MINIO_ACCESS_KEY=admin
MINIO_SECRET_KEY=Ph7eeesM.m7WteT4Cy-M
MINIO_SECURE=false

# ----------------------------------------------------------
# -------------- AI ----------------------------------------
# ----------------------------------------------------------
GOOGLE_API_KEY=
GROQ_API_KEY=
AGNO_API_KEY=
AGNO_MONITOR=true
