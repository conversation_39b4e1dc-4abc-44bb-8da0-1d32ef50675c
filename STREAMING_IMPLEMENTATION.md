# Real-time Chat Streaming with Tool Calls Implementation Plan

This document outlines the revised plan to implement a real-time, streaming-first chat experience with support for tool call visualization and robust conversation history.

## Guiding Principles

- **Streaming First:** All chat responses will be streamed from the backend by default. The non-streaming option will be removed.
- **Server-Sent Events (SSE):** We will use SSE (`text/event-stream`) for communication from the backend to the frontend. This is a standard and efficient way to handle real-time updates.
- **Structured Events:** The stream will consist of JSON-formatted events, each with a `type` and `data` payload, to allow the frontend to easily handle different kinds of information (text, tool calls, etc.).

## Backend (FastAPI)

### 1. `agent_service.py`: Always-On Streaming

- **Objective:** Refactor `chat_with_agent` to be an `AsyncGenerator` that always streams.
- **File:** `backend/app/services/agent_service.py`
- **Details:**
    - The function will be converted to an `async def` that `yields` JSON objects.
    - The `self.agent.run()` call will have `stream=True` hardcoded.
    - The generator will yield events like:
        - `{"type": "session_id", "data": "<session_id>"}`
        - `{"type": "text_chunk", "data": "<text_content>"}`
        - `{"type": "tool_start", "data": {"name": "<tool_name>", "args": {...}}}`
        - `{"type": "tool_end", "data": {"name": "<tool_name>", "output": "..."}}`
        - `{"type": "error", "data": "<error_message>"}`

### 2. `api/routes/agent.py`: SSE Endpoint

- **Objective:** Expose the chat functionality via an SSE endpoint.
- **File:** `backend/app/api/routes/agent.py`
- **Details:**
    - The `/chat` endpoint will be changed to return a `StreamingResponse`.
    - The `media_type` will be set to `text/event-stream`.
    - The endpoint will iterate through the `AsyncGenerator` from the `agent_service` and format each yielded JSON object as an SSE message (e.g., `data: <json_string>\n\n`).

### 3. `api/schemas/agent.py`: Schema Updates

- **Objective:** Clean up the API schemas to reflect the streaming-first approach.
- **File:** `backend/app/api/schemas/agent.py`
- **Details:**
    - The `stream` field will be removed from the `ChatRequest` schema.
    - The `ChatResponse` schema will be removed if it's no longer used by any endpoint.

## Frontend (Flutter)

### 1. Custom SSE Service

- **Objective:** Create a dedicated service to handle the SSE connection and event parsing, as the generated OpenAPI client is not suitable for this.
- **Details:**
    - A new Dart class (e.g., `ChatService`) will be created.
    - This service will use a package like `http` or `dio` to make the POST request to the `/chat` endpoint and keep the connection open.
    - It will listen to the response stream, parse the SSE messages, and decode the JSON data.

### 2. `chat_screen.dart`: UI Updates from Stream

- **Objective:** Rebuild the chat screen to reactively handle the event stream.
- **File:** `frontend/lib/chat/chat_screen.dart`
- **Details:**
    - The `_getBotResponse` method will be completely rewritten to use the new `ChatService`.
    - It will listen to a stream of events from the service.
    - A `StreamBuilder` will be used to update the UI based on the events:
        - **`session_id`**: Store the session ID.
        - **`text_chunk`**: Append text to the last message in the `_messages` list.
        - **`tool_start`**: Add a new `ChatMessage` to the list with a `ToolCallWidget` in a loading state.
        - **`tool_end`**: Find the corresponding `ToolCallWidget` and update it with the result.
        - **`error`**: Display an error message.

### 3. `tool_call_widget.dart` (New Widget)

- **Objective:** Create a widget to visualize tool calls.
- **File:** `frontend/lib/chat/tool_call_widget.dart` (new file)
- **Details:**
    - It will be a `StatefulWidget`.
    - It will display the tool name and arguments.
    - It will show a loading indicator while the tool is running.
    - It will display the tool's output when the `tool_end` event is received.

### 4. `chat_message.dart`: Message Model Update

- **Objective:** Adapt the `ChatMessage` to support tool calls.
- **File:** `frontend/lib/chat/chat_message.dart`
- **Details:**
    - The `ChatMessage` class will be updated to optionally contain a `ToolCall` object.
    - It will conditionally render the `ToolCallWidget` instead of the text body if a tool call is present.

### 5. Conversation History

- **Objective:** Ensure conversation history correctly displays all message types, including tool calls.
- **File:** `frontend/lib/chat/chat_screen.dart`
- **Details:**
    - The `_loadConversationHistory` function will be updated to parse tool call events from the history data.
    - The backend's `get_conversation_history` response will be checked to ensure it provides the necessary information for rendering all message types.