services:
  db:
    restart: "no"
    ports:
      - "5432:5432"

  # redis:
  #   restart: "no"
  #   ports:
  #     - "6379:6379"

  backend:
    restart: "no"
    ports:
      - "8000:8000"
    build:
      context: ./backend
      dockerfile: Dockerfile
    # command: sleep infinity  # Infinite loop to keep container alive doing nothing
    command:
      - fastapi
      - run
      - --reload
      - "app/main.py"
    develop:
      watch:
        - path: .
          action: sync
          target: /app
          ignore:
            - ./.venv
            - .venv
        - path: ./pyproject.toml
          action: rebuild
    # TODO: remove once coverage is done locally
    volumes:
      - ./htmlcov:/app/htmlcov
    environment:
      SMTP_HOST: "mailcatcher"
      SMTP_PORT: "1025"
      SMTP_TLS: "false"
      EMAILS_FROM_EMAIL: "<EMAIL>"

  # worker:
  #   restart: "no"
  #   build:
  #     context: ./backend
  #     dockerfile: Dockerfile
  #   env_file:
  #     - .env
  #   volumes:
  #     - ~/.aws/:/root/.aws:rw
  #   environment:
  #     SMTP_HOST: "mailcatcher"
  #     SMTP_PORT: "1025"
  #     SMTP_TLS: "false"
  #     EMAILS_FROM_EMAIL: "<EMAIL>"

  # minio:
  #   restart: "no"
  #   ports:
  #     - "9000:9000" # API port
  #     - "9001:9001" # Console port

  # frontend:
  #   restart: "no"
  #   ports:
  #     - "3000:3000"
  #   build:
  #     context: ./frontend
  #     dockerfile: Dockerfile
  #     args:
  #       - NEXT_PUBLIC_API_URL=http://localhost:8000
  #   environment:
  #     - NODE_ENV=development
