#!/usr/bin/env bash

# OpenAPI Setup Validation Script
# This script validates the OpenAPI generator setup and configuration

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[0;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
FRONTEND_DIR="frontend"
REQUIRED_FILES=(
    "frontend/lib/openapi_generator_config.dart"
    "frontend/build.yaml"
    "frontend/lib/config/api_config.dart"
    "frontend/lib/services/api_client_service.dart"
    "frontend/lib/services/auth_service.dart"
    "scripts/generate-client.sh"
)

REQUIRED_DEPENDENCIES=(
    "openapi_generator_annotations"
    "dio"
    "shared_preferences"
    "google_sign_in"
    "awesome_snackbar_content"
)

REQUIRED_DEV_DEPENDENCIES=(
    "openapi_generator"
    "build_runner"
)

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Function to check if command exists
command_exists() {
    command -v "$1" >/dev/null 2>&1
}

# Function to check required files
check_required_files() {
    print_status "Checking required files..."
    local missing_files=()
    
    for file in "${REQUIRED_FILES[@]}"; do
        if [ ! -f "$file" ]; then
            missing_files+=("$file")
        fi
    done
    
    if [ ${#missing_files[@]} -eq 0 ]; then
        print_success "All required files are present"
        return 0
    else
        print_error "Missing required files:"
        for file in "${missing_files[@]}"; do
            echo "  - $file"
        done
        return 1
    fi
}

# Function to check Flutter dependencies
check_flutter_dependencies() {
    print_status "Checking Flutter dependencies..."
    
    if [ ! -f "${FRONTEND_DIR}/pubspec.yaml" ]; then
        print_error "pubspec.yaml not found in ${FRONTEND_DIR}"
        return 1
    fi
    
    local missing_deps=()
    local missing_dev_deps=()
    
    # Check regular dependencies
    for dep in "${REQUIRED_DEPENDENCIES[@]}"; do
        if ! grep -q "^[[:space:]]*${dep}:" "${FRONTEND_DIR}/pubspec.yaml"; then
            missing_deps+=("$dep")
        fi
    done
    
    # Check dev dependencies
    for dep in "${REQUIRED_DEV_DEPENDENCIES[@]}"; do
        if ! grep -q "^[[:space:]]*${dep}:" "${FRONTEND_DIR}/pubspec.yaml"; then
            missing_dev_deps+=("$dep")
        fi
    done
    
    local has_errors=false
    
    if [ ${#missing_deps[@]} -gt 0 ]; then
        print_error "Missing dependencies in pubspec.yaml:"
        for dep in "${missing_deps[@]}"; do
            echo "  - $dep"
        done
        has_errors=true
    fi
    
    if [ ${#missing_dev_deps[@]} -gt 0 ]; then
        print_error "Missing dev_dependencies in pubspec.yaml:"
        for dep in "${missing_dev_deps[@]}"; do
            echo "  - $dep"
        done
        has_errors=true
    fi
    
    if [ "$has_errors" = false ]; then
        print_success "All required dependencies are present"
        return 0
    else
        return 1
    fi
}

# Function to validate configuration files
validate_config_files() {
    print_status "Validating configuration files..."
    
    # Check openapi_generator_config.dart
    if [ -f "${FRONTEND_DIR}/lib/openapi_generator_config.dart" ]; then
        if grep -q "@Openapi" "${FRONTEND_DIR}/lib/openapi_generator_config.dart" && \
           grep -q "Generator.dioNext" "${FRONTEND_DIR}/lib/openapi_generator_config.dart"; then
            print_success "openapi_generator_config.dart is properly configured"
        else
            print_warning "openapi_generator_config.dart may not be properly configured"
        fi
    fi
    
    # Check build.yaml
    if [ -f "${FRONTEND_DIR}/build.yaml" ]; then
        if grep -q "openapi_generator:" "${FRONTEND_DIR}/build.yaml" && \
           grep -q "dio-next" "${FRONTEND_DIR}/build.yaml"; then
            print_success "build.yaml is properly configured"
        else
            print_warning "build.yaml may not be properly configured"
        fi
    fi
    
    # Check if generate script is executable
    if [ -x "scripts/generate-client.sh" ]; then
        print_success "generate-client.sh is executable"
    else
        print_warning "generate-client.sh is not executable (run: chmod +x scripts/generate-client.sh)"
    fi
}

# Function to check Flutter environment
check_flutter_environment() {
    print_status "Checking Flutter environment..."
    
    if ! command_exists flutter; then
        print_error "Flutter is not installed or not in PATH"
        return 1
    fi
    
    # Check Flutter version
    local flutter_version=$(flutter --version | head -n 1)
    print_success "Flutter found: $flutter_version"
    
    # Check if we're in a Flutter project
    if [ ! -f "${FRONTEND_DIR}/pubspec.yaml" ]; then
        print_error "Not in a Flutter project directory"
        return 1
    fi
    
    return 0
}

# Function to check project structure
check_project_structure() {
    print_status "Checking project structure..."
    
    local required_dirs=(
        "${FRONTEND_DIR}/lib"
        "${FRONTEND_DIR}/lib/config"
        "${FRONTEND_DIR}/lib/services"
    )
    
    for dir in "${required_dirs[@]}"; do
        if [ ! -d "$dir" ]; then
            print_error "Required directory missing: $dir"
            return 1
        fi
    done
    
    print_success "Project structure is correct"
    return 0
}

# Function to provide recommendations
provide_recommendations() {
    print_status "Recommendations for best practices:"
    
    echo "1. Always run the backend server before generating the client"
    echo "2. Use the provided AuthService for all authentication operations"
    echo "3. Configure different environments in ApiConfig"
    echo "4. Test the generated client after each generation"
    echo "5. Keep the generated api_client directory in .gitignore"
    echo "6. Use proper error handling with try-catch blocks"
    echo "7. Implement proper token refresh logic for production"
    echo "8. Use HTTPS in production environments"
    echo "9. Regularly update openapi_generator dependencies"
    echo "10. Follow the documentation in README_OPENAPI.md"
}

# Function to run all checks
run_all_checks() {
    local has_errors=false
    
    print_status "Starting OpenAPI setup validation..."
    echo
    
    if ! check_flutter_environment; then
        has_errors=true
    fi
    echo
    
    if ! check_project_structure; then
        has_errors=true
    fi
    echo
    
    if ! check_required_files; then
        has_errors=true
    fi
    echo
    
    if ! check_flutter_dependencies; then
        has_errors=true
    fi
    echo
    
    validate_config_files
    echo
    
    provide_recommendations
    echo
    
    if [ "$has_errors" = true ]; then
        print_error "Validation completed with errors. Please fix the issues above."
        return 1
    else
        print_success "Validation completed successfully! Your OpenAPI setup is ready."
        return 0
    fi
}

# Main execution
main() {
    run_all_checks
}

# Run main function
main "$@"
