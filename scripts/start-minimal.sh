#!/bin/bash

# Load environment variables from .env file
set -a
source .env
set +a

# Force disable observability for minimal startup
export OBSERVABILITY=false

echo "Starting UniStudio in minimal mode (no monitoring services)..."

# Build compose file arguments
COMPOSE_FILES="-f docker-compose.yml -f docker-compose.override.yml"

# Enable docker compose watch for development
echo "Starting docker compose with watch mode (minimal setup)..."
docker compose $COMPOSE_FILES $PROFILES watch
