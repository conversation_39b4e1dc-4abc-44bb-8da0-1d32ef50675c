#!/usr/bin/env bash

# OpenAPI Client Generator Script
# This script generates a Dart API client from an OpenAPI specification
# using the openapi_generator package with proper Flutter integration

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[0;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
FRONTEND_DIR="frontend"
API_BASE_URL="${API_BASE_URL:-http://localhost:8000}"
OPENAPI_ENDPOINT="${API_BASE_URL}/api/v1/openapi.json"

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Function to check if command exists
command_exists() {
    command -v "$1" >/dev/null 2>&1
}

# Function to check if API server is running
check_api_server() {
    print_status "Checking if API server is running at ${API_BASE_URL}..."
    if curl -s --fail "${OPENAPI_ENDPOINT}" >/dev/null; then
        print_success "API server is running"
        return 0
    else
        print_error "API server is not running at ${API_BASE_URL}"
        print_warning "Please start your backend server first"
        return 1
    fi
}

# Function to validate dependencies
validate_dependencies() {
    print_status "Validating dependencies..."

    if ! command_exists flutter; then
        print_error "Flutter is not installed or not in PATH"
        exit 1
    fi

    if ! command_exists curl; then
        print_error "curl is not installed"
        exit 1
    fi

    print_success "All dependencies are available"
}

# Function to temporarily disable openapi dependency
disable_openapi_dependency() {
    print_status "Temporarily disabling openapi dependency..."

    # Create backup of pubspec.yaml
    cp pubspec.yaml pubspec.yaml.backup

    # Comment out openapi dependency
    sed -i.tmp 's/^[[:space:]]*openapi:/# &/' pubspec.yaml
    sed -i.tmp 's/^[[:space:]]*path: \.\/api_client/# &/' pubspec.yaml

    # Remove temporary file created by sed
    rm -f pubspec.yaml.tmp

    print_success "OpenAPI dependency temporarily disabled"
}

# Function to restore openapi dependency
restore_openapi_dependency() {
    print_status "Restoring openapi dependency..."

    # Restore from backup
    if [ -f "pubspec.yaml.backup" ]; then
        mv pubspec.yaml.backup pubspec.yaml
        print_success "OpenAPI dependency restored"
    else
        print_error "Backup file not found, manually restoring dependency"
        # Manually restore if backup is missing
        sed -i.tmp 's/^# *openapi:/    openapi:/' pubspec.yaml
        sed -i.tmp 's/^# *path: \.\/api_client/        path: .\/api_client/' pubspec.yaml
        rm -f pubspec.yaml.tmp
    fi
}

# Function to clean previous generation
clean_previous_generation() {
    print_status "Cleaning previous generation..."

    cd "${FRONTEND_DIR}"

    # Remove existing api_client directory
    if [ -d "api_client" ]; then
        rm -rf api_client
        print_success "Removed existing api_client directory"
    fi

    # Remove existing openapi.json
    if [ -f "openapi.json" ]; then
        rm -f openapi.json
        print_success "Removed existing openapi.json"
    fi

    # Remove build cache
    if [ -d ".dart_tool/build" ]; then
        rm -rf .dart_tool/build
        print_success "Removed build cache"
    fi
}

# Function to install build dependencies
install_build_dependencies() {
    print_status "Installing build dependencies..."

    # Temporarily disable openapi dependency
    disable_openapi_dependency

    # Install dependencies
    flutter pub get

    if [ $? -eq 0 ]; then
        print_success "Build dependencies installed successfully"
    else
        print_error "Failed to install build dependencies"
        restore_openapi_dependency
        exit 1
    fi
}

# Function to fix known generation issues
fix_generated_code() {
    print_status "Fixing known generation issues..."

    # Fix ValidationErrorLocInner.dart if it exists and has formatting issues
    local validation_file="api_client/lib/model/validation_error_loc_inner.dart"
    if [ -f "$validation_file" ]; then
        # Check if the file has the known formatting issue
        if grep -q "bool operator ==(Object other) => identical(this, other) || other is ValidationErrorLocInner &&$" "$validation_file"; then
            print_status "Fixing ValidationErrorLocInner formatting issue..."

            # Create a temporary file with the fix
            sed 's/bool operator ==(Object other) => identical(this, other) || other is ValidationErrorLocInner &&$/bool operator ==(Object other) => identical(this, other) || other is ValidationErrorLocInner;/' "$validation_file" > "$validation_file.tmp"
            sed 's/int get hashCode =>$/int get hashCode => runtimeType.hashCode;/' "$validation_file.tmp" > "$validation_file.tmp2"
            sed '/^[[:space:]]*\/\/ ignore: unnecessary_parenthesis$/d' "$validation_file.tmp2" > "$validation_file"

            # Clean up temporary files
            rm -f "$validation_file.tmp" "$validation_file.tmp2"

            print_success "Fixed ValidationErrorLocInner formatting issue"
        fi
    fi
}

# Function to generate API client
generate_client() {
    print_status "Generating API client..."

    # Use build_runner to generate the client
    flutter pub run build_runner build --delete-conflicting-outputs

    # Check if generation was successful (api_client directory should exist)
    if [ -d "api_client" ]; then
        print_success "API client generated successfully"

        # Fix any known generation issues
        fix_generated_code
    else
        print_error "Failed to generate API client - api_client directory not found"
        restore_openapi_dependency
        exit 1
    fi
}

# Function to verify generation
verify_generation() {
    print_status "Verifying generated client..."

    if [ ! -d "api_client" ]; then
        print_error "api_client directory was not created"
        exit 1
    fi

    if [ ! -f "api_client/pubspec.yaml" ]; then
        print_error "Generated client is missing pubspec.yaml"
        exit 1
    fi

    if [ ! -f "api_client/lib/api.dart" ]; then
        print_error "Generated client is missing main API file"
        exit 1
    fi

    # Check if main API classes exist
    local api_files=(
        "api_client/lib/api/agent_api.dart"
        "api_client/lib/api/google_api.dart"
        "api_client/lib/api/login_api.dart"
        "api_client/lib/api/users_api.dart"
    )

    for api_file in "${api_files[@]}"; do
        if [ ! -f "$api_file" ]; then
            print_warning "API file missing: $api_file"
        fi
    done

    print_success "Generated client verification passed"
}

# Function to install final dependencies
install_final_dependencies() {
    print_status "Installing final dependencies..."

    # Restore openapi dependency
    restore_openapi_dependency

    # Install all dependencies including the generated client
    flutter pub get

    if [ $? -eq 0 ]; then
        print_success "Final dependencies installed successfully"
    else
        print_error "Failed to install final dependencies"
        exit 1
    fi

    # Install generated client dependencies
    if [ -d "api_client" ]; then
        cd api_client
        flutter pub get
        if [ $? -eq 0 ]; then
            print_success "Generated client dependencies installed"
        else
            print_warning "Failed to install generated client dependencies"
        fi
        cd ..
    fi
}

# Function to cleanup on error
cleanup_on_error() {
    print_error "Generation failed, cleaning up..."

    # Restore pubspec.yaml if backup exists
    if [ -f "pubspec.yaml.backup" ]; then
        mv pubspec.yaml.backup pubspec.yaml
        print_status "Restored original pubspec.yaml"
    fi

    # Remove any temporary files
    rm -f pubspec.yaml.tmp
}

# Main execution
main() {
    print_status "Starting OpenAPI client generation..."

    # Set up error handling
    trap cleanup_on_error ERR

    # Validate environment
    validate_dependencies

    # Check if API server is running
    if ! check_api_server; then
        exit 1
    fi

    # Clean previous generation
    clean_previous_generation

    # Install build dependencies (without openapi dependency)
    install_build_dependencies

    # Generate client
    generate_client

    # Verify generation
    verify_generation

    # Install final dependencies (with openapi dependency)
    install_final_dependencies

    print_success "OpenAPI client generation completed successfully!"
    print_status "Generated client is available in the 'api_client' directory"
    print_status "You can now use the AuthService and ApiClientService in your Flutter app"
}

# Run main function
main "$@"