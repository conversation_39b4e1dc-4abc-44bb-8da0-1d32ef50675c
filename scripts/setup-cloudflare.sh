#!/bin/bash

# Cloudflare DNS Setup Script
# This script helps configure DNS records for your domain

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

echo -e "${BLUE}🌐 Cloudflare DNS Setup for Tax Application${NC}"
echo "=================================================="

# Configuration
INSTANCE_NAME="edubase-dev"
INSTANCE_ZONE="asia-southeast1-a"

# Function to log messages
log() {
    echo -e "${GREEN}[$(date '+%Y-%m-%d %H:%M:%S')] $1${NC}"
}

error() {
    echo -e "${RED}[$(date '+%Y-%m-%d %H:%M:%S')] ERROR: $1${NC}"
}

warning() {
    echo -e "${YELLOW}[$(date '+%Y-%m-%d %H:%M:%S')] WARNING: $1${NC}"
}

# Get instance IP
log "Getting instance IP address..."
INSTANCE_IP=$(gcloud compute instances describe $INSTANCE_NAME \
    --zone=$INSTANCE_ZONE \
    --format='get(networkInterfaces[0].accessConfigs[0].natIP)')

if [ -z "$INSTANCE_IP" ]; then
    error "Failed to get instance IP address"
    exit 1
fi

log "Instance IP: $INSTANCE_IP"

# Prompt for domain
echo ""
read -p "Enter your domain name (e.g., example.com): " DOMAIN
if [ -z "$DOMAIN" ]; then
    error "Domain name is required"
    exit 1
fi

echo ""
echo -e "${YELLOW}Choose authentication method:${NC}"
echo "1. Login with browser (recommended)"
echo "2. Use API token"
read -p "Choose option (1 or 2): " AUTH_METHOD

if [ "$AUTH_METHOD" = "1" ]; then
    log "Authenticating with Cloudflare..."
    wrangler login
elif [ "$AUTH_METHOD" = "2" ]; then
    read -p "Enter your Cloudflare API token: " API_TOKEN
    if [ -z "$API_TOKEN" ]; then
        error "API token is required"
        exit 1
    fi
    export CLOUDFLARE_API_TOKEN="$API_TOKEN"
    log "API token set as environment variable"
else
    error "Invalid option selected"
    exit 1
fi

# Function to get zone ID using Cloudflare API
get_zone_id() {
    local domain=$1
    local response

    if [ "$AUTH_METHOD" = "1" ]; then
        # For browser login, we need to get token differently
        error "Browser login method requires manual zone ID lookup"
        echo "Please go to Cloudflare dashboard and find your zone ID for $domain"
        read -p "Enter your zone ID: " ZONE_ID
    else
        # Use API to get zone ID
        response=$(curl -s -X GET "https://api.cloudflare.com/client/v4/zones?name=$domain" \
            -H "Authorization: Bearer $CLOUDFLARE_API_TOKEN" \
            -H "Content-Type: application/json")

        ZONE_ID=$(echo "$response" | grep -o '"id":"[^"]*"' | head -1 | cut -d'"' -f4)
    fi
}

# Get zone ID
log "Getting zone ID for $DOMAIN..."
get_zone_id "$DOMAIN"

if [ -z "$ZONE_ID" ]; then
    error "Zone not found for domain $DOMAIN"
    echo "Please ensure:"
    echo "1. Domain is added to your Cloudflare account"
    echo "2. API token has Zone:Read permissions"
    exit 1
fi

log "Zone ID: $ZONE_ID"

# Create DNS records
log "Creating DNS records..."

echo ""
echo -e "${BLUE}Creating A record for $DOMAIN -> $INSTANCE_IP${NC}"
RESPONSE1=$(curl -s -X POST "https://api.cloudflare.com/client/v4/zones/$ZONE_ID/dns_records" \
     -H "Authorization: Bearer $CLOUDFLARE_API_TOKEN" \
     -H "Content-Type: application/json" \
     --data "{\"type\":\"A\",\"name\":\"@\",\"content\":\"$INSTANCE_IP\",\"ttl\":1}")

echo "$RESPONSE1" | grep -q '"success":true' && log "✅ A record created for $DOMAIN" || warning "❌ Failed to create A record for $DOMAIN"

echo ""
echo -e "${BLUE}Creating A record for www.$DOMAIN -> $INSTANCE_IP${NC}"
RESPONSE2=$(curl -s -X POST "https://api.cloudflare.com/client/v4/zones/$ZONE_ID/dns_records" \
     -H "Authorization: Bearer $CLOUDFLARE_API_TOKEN" \
     -H "Content-Type: application/json" \
     --data "{\"type\":\"A\",\"name\":\"www\",\"content\":\"$INSTANCE_IP\",\"ttl\":1}")

echo "$RESPONSE2" | grep -q '"success":true' && log "✅ A record created for www.$DOMAIN" || warning "❌ Failed to create A record for www.$DOMAIN"

log "DNS records creation completed!"

echo ""
echo -e "${GREEN}🎉 Setup Complete!${NC}"
echo ""
echo -e "${YELLOW}Next Steps:${NC}"
echo "1. Wait 5-10 minutes for DNS propagation"
echo "2. Update your .env.production file with:"
echo "   - Replace 'your-domain.com' with '$DOMAIN'"
echo "   - Set secure passwords for all 'CHANGE-THIS-TO-' values"
echo "3. Push to GitHub secret:"
echo "   cat .env.production | gh secret set PRODUCTION_ENV_FILE"
echo "4. Deploy your application:"
echo "   - Push to develop branch, or"
echo "   - Run GitHub Actions manually with domain: $DOMAIN"
echo ""
echo -e "${BLUE}Test DNS propagation:${NC}"
echo "nslookup $DOMAIN"
echo "nslookup www.$DOMAIN"
echo ""
echo -e "${BLUE}Your domain: $DOMAIN${NC}"
echo -e "${BLUE}Instance IP: $INSTANCE_IP${NC}"
