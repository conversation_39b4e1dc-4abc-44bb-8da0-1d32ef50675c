name: Dart CI

on: push
#  push:
#    branches: [ master ]
#  pull_request:
#    branches: [ master ]

env:
  flutter_channel: 'stable' # or: 'dev' or 'beta'
  flutter_version: '2.2.2'

jobs:
  build:

    runs-on: ubuntu-latest

    steps:
    - uses: actions/checkout@v3

    - uses: subosito/flutter-action@v2
      with:
        channel: 'stable' # or: 'beta', 'dev' or 'master'

    - uses: actions/setup-java@v1
      with:
        java-version: '12.x'

    - run: flutter pub get
      working-directory: best_flutter_ui_templates
    # - run: flutter test

    - name: build APK
      run: flutter build apk
      working-directory: best_flutter_ui_templates

    - name: upload APK
      uses: actions/upload-artifact@v1
      with:
        name: apk
        path: best_flutter_ui_templates/build/app/outputs/apk/release/app-release.apk
