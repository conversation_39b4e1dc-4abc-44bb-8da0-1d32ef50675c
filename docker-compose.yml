services:
  db:
    image: postgres:17
    restart: always
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U ${POSTGRES_USER} -d ${POSTGRES_DB}"]
      interval: 10s
      retries: 5
      start_period: 30s
      timeout: 10s
    volumes:
      - app-db-data:/var/lib/postgresql/data/pgdata
    env_file:
      - .env
    environment:
      - PGDATA=/var/lib/postgresql/data/pgdata
      - POSTGRES_PASSWORD=${POSTGRES_PASSWORD?Variable not set}
      - POSTGRES_USER=${POSTGRES_USER?Variable not set}
      - POSTGRES_DB=${POSTGRES_DB?Variable not set}
    networks:
      - default

  # redis:
  #   image: redis:8
  #   restart: always
  #   volumes:
  #     - app-redis-data:/data

  prestart:
    image: "${STACK_NAME}-${DOCKER_IMAGE_BACKEND?Variable not set}:${TAG-latest}"
    build:
      context: ./backend
    depends_on:
      db:
        condition: service_healthy
        restart: true
    command: bash scripts/prestart.sh
    env_file:
      - .env
    environment:
      - FRONTEND_HOST=${FRONTEND_HOST?Variable not set}
      - ENVIRONMENT=${ENVIRONMENT}
      - BACKEND_CORS_ORIGINS=${BACKEND_CORS_ORIGINS}
      - SECRET_KEY=${SECRET_KEY?Variable not set}
      - FIRST_SUPERUSER=${FIRST_SUPERUSER?Variable not set}
      - FIRST_SUPERUSER_PASSWORD=${FIRST_SUPERUSER_PASSWORD?Variable not set}
      - SMTP_HOST=${SMTP_HOST}
      - SMTP_USER=${SMTP_USER}
      - SMTP_PASSWORD=${SMTP_PASSWORD}
      - EMAILS_FROM_EMAIL=${EMAILS_FROM_EMAIL}
      - POSTGRES_SERVER=db
      - POSTGRES_PORT=${POSTGRES_PORT}
      - POSTGRES_DB=${POSTGRES_DB}
      - POSTGRES_USER=${POSTGRES_USER?Variable not set}
      - POSTGRES_PASSWORD=${POSTGRES_PASSWORD?Variable not set}
      - SENTRY_DSN=${SENTRY_DSN}
    networks:
      - default

  backend:
    image: "${STACK_NAME}-${DOCKER_IMAGE_BACKEND?Variable not set}:${TAG-latest}"
    restart: always
    depends_on:
      db:
        condition: service_healthy
        restart: true
      prestart:
        condition: service_completed_successfully
    env_file:
      - .env
    environment:
      - FRONTEND_HOST=${FRONTEND_HOST?Variable not set}
      - ENVIRONMENT=${ENVIRONMENT}
      - BACKEND_CORS_ORIGINS=${BACKEND_CORS_ORIGINS}
      - SECRET_KEY=${SECRET_KEY?Variable not set}
      - FIRST_SUPERUSER=${FIRST_SUPERUSER?Variable not set}
      - FIRST_SUPERUSER_PASSWORD=${FIRST_SUPERUSER_PASSWORD?Variable not set}
      - SMTP_HOST=${SMTP_HOST}
      - SMTP_USER=${SMTP_USER}
      - SMTP_PASSWORD=${SMTP_PASSWORD}
      - EMAILS_FROM_EMAIL=${EMAILS_FROM_EMAIL}
      - POSTGRES_SERVER=db
      - POSTGRES_PORT=${POSTGRES_PORT}
      - POSTGRES_DB=${POSTGRES_DB}
      - POSTGRES_USER=${POSTGRES_USER?Variable not set}
      - POSTGRES_PASSWORD=${POSTGRES_PASSWORD?Variable not set}
      - SENTRY_DSN=${SENTRY_DSN}
    build:
      context: ./backend
    networks:
      - default

  # worker:
  #   deploy:
  #     mode: replicated
  #     replicas: 1
  #   image: "${STACK_NAME}-${DOCKER_IMAGE_BACKEND?Variable not set}:${TAG-latest}"
  #   restart: always
  #   command: celery -A app.worker.celery_app worker --loglevel=info
  #   depends_on:
  #     db:
  #       condition: service_healthy
  #       restart: true
  #     prestart:
  #       condition: service_completed_successfully
  #     redis:
  #       condition: service_started
  #   env_file:
  #     - .env
  #   environment:
  #     - FRONTEND_HOST=${FRONTEND_HOST?Variable not set}
  #     - ENVIRONMENT=${ENVIRONMENT}
  #     - BACKEND_CORS_ORIGINS=${BACKEND_CORS_ORIGINS}
  #     - SECRET_KEY=${SECRET_KEY?Variable not set}
  #     - FIRST_SUPERUSER=${FIRST_SUPERUSER?Variable not set}
  #     - FIRST_SUPERUSER_PASSWORD=${FIRST_SUPERUSER_PASSWORD?Variable not set}
  #     - SMTP_HOST=${SMTP_HOST}
  #     - SMTP_USER=${SMTP_USER}
  #     - SMTP_PASSWORD=${SMTP_PASSWORD}
  #     - EMAILS_FROM_EMAIL=${EMAILS_FROM_EMAIL}
  #     - POSTGRES_SERVER=db
  #     - POSTGRES_PORT=${POSTGRES_PORT}
  #     - POSTGRES_DB=${POSTGRES_DB}
  #     - POSTGRES_USER=${POSTGRES_USER?Variable not set}
  #     - POSTGRES_PASSWORD=${POSTGRES_PASSWORD?Variable not set}
  #     - SENTRY_DSN=${SENTRY_DSN}
  #   build:
  #     context: ./backend
  #   networks:
  #     - default

  # scheduler:
  #   deploy:
  #     mode: replicated
  #     replicas: 1
  #   image: "${STACK_NAME}-${DOCKER_IMAGE_BACKEND?Variable not set}:${TAG-latest}"
  #   restart: always
  #   command: celery -A app.worker.celery_app beat --loglevel=debug
  #   depends_on:
  #     db:
  #       condition: service_healthy
  #       restart: true
  #     prestart:
  #       condition: service_completed_successfully
  #     redis:
  #       condition: service_started
  #   env_file:
  #     - .env
  #   environment:
  #     - FRONTEND_HOST=${FRONTEND_HOST?Variable not set}
  #     - ENVIRONMENT=${ENVIRONMENT}
  #     - BACKEND_CORS_ORIGINS=${BACKEND_CORS_ORIGINS}
  #     - SECRET_KEY=${SECRET_KEY?Variable not set}
  #     - FIRST_SUPERUSER=${FIRST_SUPERUSER?Variable not set}
  #     - FIRST_SUPERUSER_PASSWORD=${FIRST_SUPERUSER_PASSWORD?Variable not set}
  #     - SMTP_HOST=${SMTP_HOST}
  #     - SMTP_USER=${SMTP_USER}
  #     - SMTP_PASSWORD=${SMTP_PASSWORD}
  #     - EMAILS_FROM_EMAIL=${EMAILS_FROM_EMAIL}
  #     - POSTGRES_SERVER=db
  #     - POSTGRES_PORT=${POSTGRES_PORT}
  #     - POSTGRES_DB=${POSTGRES_DB}
  #     - POSTGRES_USER=${POSTGRES_USER?Variable not set}
  #     - POSTGRES_PASSWORD=${POSTGRES_PASSWORD?Variable not set}
  #     - SENTRY_DSN=${SENTRY_DSN}
  #   build:
  #     context: ./backend
  #   networks:
  #     - default

  # minio:
  #   image: minio/minio:latest
  #   restart: always
  #   volumes:
  #     - app-minio-data:/data
  #   environment:
  #     - MINIO_ROOT_USER=${MINIO_ROOT_USER:-minioadmin}
  #     - MINIO_ROOT_PASSWORD=${MINIO_ROOT_PASSWORD:-minioadmin}
  #   command: server /data --console-address ":9001"
  #   networks:
  #     - default

  # frontend:
  #   image: "${STACK_NAME}-${DOCKER_IMAGE_FRONTEND?Variable not set}:${TAG-latest}"
  #   restart: always
  #   build:
  #     context: ./frontend
  #     dockerfile: Dockerfile
  #     args:
  #       - NEXT_PUBLIC_API_URL=${BACKEND_HOST:-http://backend:8000}
  #   networks:
  #     - default

volumes:
  app-db-data:
  app-redis-data:
  app-minio-data:
