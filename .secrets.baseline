{"version": "1.5.0", "plugins_used": [{"name": "ArtifactoryDetector"}, {"name": "AWSKeyDetector"}, {"name": "AzureStorageKeyDetector"}, {"name": "Base64HighEntropyString", "limit": 4.5}, {"name": "BasicAuthDetector"}, {"name": "CloudantDetector"}, {"name": "DiscordBotTokenDetector"}, {"name": "GitHubTokenDetector"}, {"name": "GitLabTokenDetector"}, {"name": "HexHighEntropyString", "limit": 3.0}, {"name": "IbmCloudIamDetector"}, {"name": "IbmCosHmacDetector"}, {"name": "IPPublicDetector"}, {"name": "JwtTokenDetector"}, {"name": "KeywordDetector", "keyword_exclude": ""}, {"name": "MailchimpDetector"}, {"name": "NpmDetector"}, {"name": "OpenAIDetector"}, {"name": "PrivateKeyDetector"}, {"name": "PypiTokenDetector"}, {"name": "SendGridDetector"}, {"name": "SlackDetector"}, {"name": "SoftlayerDetector"}, {"name": "SquareOAuthDetector"}, {"name": "StripeDetector"}, {"name": "TelegramBotTokenDetector"}, {"name": "TwilioKeyDetector"}], "filters_used": [{"path": "detect_secrets.filters.allowlist.is_line_allowlisted"}, {"path": "detect_secrets.filters.common.is_baseline_file", "filename": ".secrets.baseline"}, {"path": "detect_secrets.filters.common.is_ignored_due_to_verification_policies", "min_level": 2}, {"path": "detect_secrets.filters.heuristic.is_indirect_reference"}, {"path": "detect_secrets.filters.heuristic.is_likely_id_string"}, {"path": "detect_secrets.filters.heuristic.is_lock_file"}, {"path": "detect_secrets.filters.heuristic.is_not_alphanumeric_string"}, {"path": "detect_secrets.filters.heuristic.is_potential_uuid"}, {"path": "detect_secrets.filters.heuristic.is_prefixed_with_dollar_sign"}, {"path": "detect_secrets.filters.heuristic.is_sequential_string"}, {"path": "detect_secrets.filters.heuristic.is_swagger_file"}, {"path": "detect_secrets.filters.heuristic.is_templated_secret"}], "results": {".env": [{"type": "Secret Keyword", "filename": ".env", "hashed_secret": "43ca237ba1c9f6c315455b3c177a24b986ae2c3f", "is_verified": false, "line_number": 184}, {"type": "Base64 High Entropy String", "filename": ".env", "hashed_secret": "525e1e44220893a7fe56573de807104c9109d2bd", "is_verified": false, "line_number": 247}, {"type": "Secret Keyword", "filename": ".env", "hashed_secret": "525e1e44220893a7fe56573de807104c9109d2bd", "is_verified": false, "line_number": 247}], "backend/app/alembic/versions/4e5fa7368e94_init_db.py": [{"type": "Hex High Entropy String", "filename": "backend/app/alembic/versions/4e5fa7368e94_init_db.py", "hashed_secret": "c77694236cb8a422f99e91a2d41aceb86c61123f", "is_verified": false, "line_number": 14}], "backend/app/alembic/versions/60f058f666fb_add_seed_version_table.py": [{"type": "Hex High Entropy String", "filename": "backend/app/alembic/versions/60f058f666fb_add_seed_version_table.py", "hashed_secret": "0a1c820cdc7c39082104d4935a35819a1e649ea7", "is_verified": false, "line_number": 15}], "backend/app/alembic/versions/66d17ce826f8_add_implemented_at_and_implemented_by_.py": [{"type": "Hex High Entropy String", "filename": "backend/app/alembic/versions/66d17ce826f8_add_implemented_at_and_implemented_by_.py", "hashed_secret": "0a1c820cdc7c39082104d4935a35819a1e649ea7", "is_verified": false, "line_number": 14}, {"type": "Hex High Entropy String", "filename": "backend/app/alembic/versions/66d17ce826f8_add_implemented_at_and_implemented_by_.py", "hashed_secret": "1dd6d89cf4f053bd45bb6b5f743f1b24b7897d83", "is_verified": false, "line_number": 15}], "backend/app/alembic/versions/6c457300cec5_update_task_history.py": [{"type": "Hex High Entropy String", "filename": "backend/app/alembic/versions/6c457300cec5_update_task_history.py", "hashed_secret": "6397b7ecec673b27c91046cbd27292f81b5e5252", "is_verified": false, "line_number": 15}], "backend/app/alembic/versions/80e3ae75f95a_remove_assigned_task.py": [{"type": "Hex High Entropy String", "filename": "backend/app/alembic/versions/80e3ae75f95a_remove_assigned_task.py", "hashed_secret": "481bf13c478f1c7423d6fcc4d6f5c9e198abfcc0", "is_verified": false, "line_number": 14}, {"type": "Hex High Entropy String", "filename": "backend/app/alembic/versions/80e3ae75f95a_remove_assigned_task.py", "hashed_secret": "86288fff834e4dd55236656b34f44894ee146fb8", "is_verified": false, "line_number": 15}], "backend/app/alembic/versions/9fe1d4c59027_add_service_savings_models.py": [{"type": "Hex High Entropy String", "filename": "backend/app/alembic/versions/9fe1d4c59027_add_service_savings_models.py", "hashed_secret": "0cf0e36f76e3d239e7b643895b560c3ac4b354ce", "is_verified": false, "line_number": 14}, {"type": "Hex High Entropy String", "filename": "backend/app/alembic/versions/9fe1d4c59027_add_service_savings_models.py", "hashed_secret": "6397b7ecec673b27c91046cbd27292f81b5e5252", "is_verified": false, "line_number": 15}], "backend/app/alembic/versions/a085fc2f3174_init_schedule_task_buitlin_tools.py": [{"type": "Hex High Entropy String", "filename": "backend/app/alembic/versions/a085fc2f3174_init_schedule_task_buitlin_tools.py", "hashed_secret": "66486f027f5198a760b17b9c3fe2faf5922a3d69", "is_verified": false, "line_number": 16}, {"type": "Hex High Entropy String", "filename": "backend/app/alembic/versions/a085fc2f3174_init_schedule_task_buitlin_tools.py", "hashed_secret": "d9c215ad3e48ad51b1c9b8519b7bb0dd87d0bb31", "is_verified": false, "line_number": 17}], "backend/app/alembic/versions/a726acf48b33_add_message_indexing.py": [{"type": "Hex High Entropy String", "filename": "backend/app/alembic/versions/a726acf48b33_add_message_indexing.py", "hashed_secret": "c92039fa4365fb16e39fcfb49909faf5bcd233d1", "is_verified": false, "line_number": 14}], "backend/app/alembic/versions/a824fc066ab3_revamp_task_table.py": [{"type": "Hex High Entropy String", "filename": "backend/app/alembic/versions/a824fc066ab3_revamp_task_table.py", "hashed_secret": "86288fff834e4dd55236656b34f44894ee146fb8", "is_verified": false, "line_number": 14}], "backend/app/alembic/versions/b765f9607f2a_fix_array_types_to_use_postgresql_.py": [{"type": "Hex High Entropy String", "filename": "backend/app/alembic/versions/b765f9607f2a_fix_array_types_to_use_postgresql_.py", "hashed_secret": "6397b7ecec673b27c91046cbd27292f81b5e5252", "is_verified": false, "line_number": 14}, {"type": "Hex High Entropy String", "filename": "backend/app/alembic/versions/b765f9607f2a_fix_array_types_to_use_postgresql_.py", "hashed_secret": "66486f027f5198a760b17b9c3fe2faf5922a3d69", "is_verified": false, "line_number": 15}], "backend/app/alembic/versions/c40620e42e2e_.py": [{"type": "Hex High Entropy String", "filename": "backend/app/alembic/versions/c40620e42e2e_.py", "hashed_secret": "0cf0e36f76e3d239e7b643895b560c3ac4b354ce", "is_verified": false, "line_number": 15}], "backend/app/alembic/versions/db745f7c4ad4_remove_orphaned_ecs_cluster_resources.py": [{"type": "Hex High Entropy String", "filename": "backend/app/alembic/versions/db745f7c4ad4_remove_orphaned_ecs_cluster_resources.py", "hashed_secret": "96c939fbf42e53b76d0ae6d2898d28e651080239", "is_verified": false, "line_number": 15}], "backend/app/alembic/versions/dc9fd7124a3e_consolidate_ecs_cluster_to_ecs_type.py": [{"type": "Hex High Entropy String", "filename": "backend/app/alembic/versions/dc9fd7124a3e_consolidate_ecs_cluster_to_ecs_type.py", "hashed_secret": "96c939fbf42e53b76d0ae6d2898d28e651080239", "is_verified": false, "line_number": 14}], "backend/app/alembic/versions/e28b969d5475_add_celery_id.py": [{"type": "Hex High Entropy String", "filename": "backend/app/alembic/versions/e28b969d5475_add_celery_id.py", "hashed_secret": "d9c215ad3e48ad51b1c9b8519b7bb0dd87d0bb31", "is_verified": false, "line_number": 14}], "backend/app/alembic/versions/eddc364cd3e1_add_runtime.py": [{"type": "Hex High Entropy String", "filename": "backend/app/alembic/versions/eddc364cd3e1_add_runtime.py", "hashed_secret": "481bf13c478f1c7423d6fcc4d6f5c9e198abfcc0", "is_verified": false, "line_number": 15}], "backend/app/alembic/versions/fb1174ab0135_remove_technical_guidances_and_document_.py": [{"type": "Hex High Entropy String", "filename": "backend/app/alembic/versions/fb1174ab0135_remove_technical_guidances_and_document_.py", "hashed_secret": "1dd6d89cf4f053bd45bb6b5f743f1b24b7897d83", "is_verified": false, "line_number": 14}, {"type": "Hex High Entropy String", "filename": "backend/app/alembic/versions/fb1174ab0135_remove_technical_guidances_and_document_.py", "hashed_secret": "c77694236cb8a422f99e91a2d41aceb86c61123f", "is_verified": false, "line_number": 15}], "backend/app/tests/api/routes/test_login.py": [{"type": "Secret Keyword", "filename": "backend/app/tests/api/routes/test_login.py", "hashed_secret": "6c03ac0ea7241c3b2e2b7d54ff1db5f5539dc198", "is_verified": false, "line_number": 30}, {"type": "Secret Keyword", "filename": "backend/app/tests/api/routes/test_login.py", "hashed_secret": "cdb0e76c1a69873cbdcdbe0a142d56c023dc9f22", "is_verified": false, "line_number": 108}], "docs/workspace/1. workspace.md": [{"type": "Secret Keyword", "filename": "docs/workspace/1. workspace.md", "hashed_secret": "5baa61e4c9b93f3f0682250b6cf8331b7ee68fd8", "is_verified": false, "line_number": 157}], "frontend/.env": [{"type": "Base64 High Entropy String", "filename": "frontend/.env", "hashed_secret": "7d7b875fa620f3088d4f274fa1188e71479881db", "is_verified": false, "line_number": 3}], "payment/app/alembic.ini": [{"type": "Basic Auth Credentials", "filename": "payment/app/alembic.ini", "hashed_secret": "9d4e1e23bd5b727046a9e3b4b7db57bd8d6ee684", "is_verified": false, "line_number": 66}], "payment/app/alembic/versions/a9913deb2529_init_payment_models.py": [{"type": "Hex High Entropy String", "filename": "payment/app/alembic/versions/a9913deb2529_init_payment_models.py", "hashed_secret": "84c1493350af9006803f5ef0c319b2958f45cfb5", "is_verified": false, "line_number": 15}], "payment/app/alembic/versions/bff70cfe7c0b_update_user_id_unique.py": [{"type": "Hex High Entropy String", "filename": "payment/app/alembic/versions/bff70cfe7c0b_update_user_id_unique.py", "hashed_secret": "80d9a6d72304bced240cc8292a4ec2fb492987d6", "is_verified": false, "line_number": 15}], "payment/app/alembic/versions/d3249a9b605e_add_form_collecting_upgrade.py": [{"type": "Hex High Entropy String", "filename": "payment/app/alembic/versions/d3249a9b605e_add_form_collecting_upgrade.py", "hashed_secret": "c94b57b623ab6bceae2e96fa168e3d9511438d16", "is_verified": false, "line_number": 14}], "payment/app/alembic/versions/e1a46993846f_add_enterprise_enquiries_table.py": [{"type": "Hex High Entropy String", "filename": "payment/app/alembic/versions/e1a46993846f_add_enterprise_enquiries_table.py", "hashed_secret": "80d9a6d72304bced240cc8292a4ec2fb492987d6", "is_verified": false, "line_number": 14}, {"type": "Hex High Entropy String", "filename": "payment/app/alembic/versions/e1a46993846f_add_enterprise_enquiries_table.py", "hashed_secret": "84c1493350af9006803f5ef0c319b2958f45cfb5", "is_verified": false, "line_number": 15}]}, "generated_at": "2025-06-21T14:36:54Z"}