# OpenAPI Generator Best Practices

This document outlines the best practices for using the `openapi_generator` package in a Flutter project.

## 1. Overview

The `openapi_generator` package is a powerful tool for generating a complete and robust API client from an OpenAPI specification. By following these best practices, you can ensure that your generated client is maintainable, efficient, and always in sync with your API.

## 2. Recommended Configuration

### `pubspec.yaml`

Your `pubspec.yaml` file should include the following dependencies:

```yaml
dependencies:
  openapi_generator_annotations: ^<latest_version>

dev_dependencies:
  openapi_generator: ^<latest_version>
  build_runner: ^<latest_version>
```

### `build.yaml`

Create a `build.yaml` file in the root of your `frontend` directory to configure the generator:

```yaml
targets:
  $default:
    builders:
      openapi_generator:
        options:
          input_spec: "openapi.json"
          generator: "dio-next"
          output_directory: "api_client"
          run_source_gen_on_output: true
```

### `openapi_generator_config.dart`

Create a file to house the `@Openapi` annotation, which provides additional configuration options:

```dart
import 'package:openapi_generator_annotations/openapi_generator_annotations.dart';

@Openapi(
  additionalProperties: DioProperties(
    pubName: 'api_client',
    pubAuthor: 'Your Name',
  ),
  inputSpec: RemoteSpec(path: 'openapi.json'),
  generatorName: Generator.dioNext,
  runSourceGenOnOutput: true,
  outputDirectory: 'api_client',
)
class OpenApiGeneratorConfig {}
```

## 3. Generating the Client

To generate the client, run the following command:

```bash
flutter pub run build_runner build --delete-conflicting-outputs
```

This will generate a new Dart package in the specified `output_directory`.

## 4. Comparison with Your Current Setup

- **Dependencies**: Your `pubspec.yaml` was missing the `openapi_generator_annotations` package.
- **Configuration**: You were not using a `build.yaml` file or an annotated class, which meant you were relying on the default configuration.
- **Generator**: You were using the `dio` generator, but `dio-next` is recommended for its support of null safety.

By adopting the recommended configuration, you will have a more robust and maintainable API client.