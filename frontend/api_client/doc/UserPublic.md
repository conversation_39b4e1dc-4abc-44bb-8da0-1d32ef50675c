# api_client.model.UserPublic

## Load the model package
```dart
import 'package:api_client/api.dart';
```

## Properties
Name | Type | Description | Notes
------------ | ------------- | ------------- | -------------
**email** | **String** |  | 
**id** | **String** |  | 
**isActive** | **bool** |  | [optional] [default to true]
**isSuperuser** | **bool** |  | [optional] [default to false]
**fullName** | **String** |  | [optional] 
**avatarUrl** | **String** |  | [optional] 
**lastLoginTime** | [**DateTime**](DateTime.md) |  | [optional] 

[[Back to Model list]](../README.md#documentation-for-models) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to README]](../README.md)


