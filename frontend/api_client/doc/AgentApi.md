# api_client.api.AgentApi

## Load the API package
```dart
import 'package:api_client/api.dart';
```

All URIs are relative to *http://localhost*

Method | HTTP request | Description
------------- | ------------- | -------------
[**agentChatWithAgent**](AgentApi.md#agentchatwithagent) | **POST** /api/v1/agent/chat | Chat With Agent
[**agentGetConversationHistory**](AgentApi.md#agentgetconversationhistory) | **GET** /api/v1/agent/conversations/{session_id} | Get Conversation History
[**agentGetConversations**](AgentApi.md#agentgetconversations) | **GET** /api/v1/agent/conversations | Get Conversations


# **agentChatWithAgent**
> JsonObject agentChatWithAgent(message, sessionId, images)

Chat With Agent

Chat with the YouRAI agent with multimodal input support (text + optional images)

### Example
```dart
import 'package:api_client/api.dart';
// TODO Configure OAuth2 access token for authorization: OAuth2PasswordBearer
//defaultApiClient.getAuthentication<OAuth>('OAuth2PasswordBearer').accessToken = 'YOUR_ACCESS_TOKEN';

final api = ApiClient().getAgentApi();
final String message = message_example; // String | 
final String sessionId = sessionId_example; // String | 
final BuiltList<MultipartFile> images = /path/to/file.txt; // BuiltList<MultipartFile> | 

try {
    final response = api.agentChatWithAgent(message, sessionId, images);
    print(response);
} catch on DioException (e) {
    print('Exception when calling AgentApi->agentChatWithAgent: $e\n');
}
```

### Parameters

Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **message** | **String**|  | 
 **sessionId** | **String**|  | [optional] 
 **images** | [**BuiltList&lt;MultipartFile&gt;**](MultipartFile.md)|  | [optional] 

### Return type

[**JsonObject**](JsonObject.md)

### Authorization

[OAuth2PasswordBearer](../README.md#OAuth2PasswordBearer)

### HTTP request headers

 - **Content-Type**: multipart/form-data
 - **Accept**: application/json

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **agentGetConversationHistory**
> Conversation agentGetConversationHistory(sessionId)

Get Conversation History

Get the history of a specific conversation session

### Example
```dart
import 'package:api_client/api.dart';
// TODO Configure OAuth2 access token for authorization: OAuth2PasswordBearer
//defaultApiClient.getAuthentication<OAuth>('OAuth2PasswordBearer').accessToken = 'YOUR_ACCESS_TOKEN';

final api = ApiClient().getAgentApi();
final String sessionId = sessionId_example; // String | 

try {
    final response = api.agentGetConversationHistory(sessionId);
    print(response);
} catch on DioException (e) {
    print('Exception when calling AgentApi->agentGetConversationHistory: $e\n');
}
```

### Parameters

Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **sessionId** | **String**|  | 

### Return type

[**Conversation**](Conversation.md)

### Authorization

[OAuth2PasswordBearer](../README.md#OAuth2PasswordBearer)

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: application/json

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **agentGetConversations**
> BuiltList<String> agentGetConversations()

Get Conversations

Get all conversation session IDs for the current user

### Example
```dart
import 'package:api_client/api.dart';
// TODO Configure OAuth2 access token for authorization: OAuth2PasswordBearer
//defaultApiClient.getAuthentication<OAuth>('OAuth2PasswordBearer').accessToken = 'YOUR_ACCESS_TOKEN';

final api = ApiClient().getAgentApi();

try {
    final response = api.agentGetConversations();
    print(response);
} catch on DioException (e) {
    print('Exception when calling AgentApi->agentGetConversations: $e\n');
}
```

### Parameters
This endpoint does not need any parameter.

### Return type

**BuiltList&lt;String&gt;**

### Authorization

[OAuth2PasswordBearer](../README.md#OAuth2PasswordBearer)

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: application/json

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

