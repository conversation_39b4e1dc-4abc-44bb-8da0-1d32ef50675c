# api_client.model.UserUpdate

## Load the model package
```dart
import 'package:api_client/api.dart';
```

## Properties
Name | Type | Description | Notes
------------ | ------------- | ------------- | -------------
**email** | **String** |  | [optional] 
**isActive** | **bool** |  | [optional] [default to true]
**isSuperuser** | **bool** |  | [optional] [default to false]
**fullName** | **String** |  | [optional] 
**avatarUrl** | **String** |  | [optional] 
**lastLoginTime** | [**DateTime**](DateTime.md) |  | [optional] 
**password** | **String** |  | [optional] 

[[Back to Model list]](../README.md#documentation-for-models) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to README]](../README.md)


