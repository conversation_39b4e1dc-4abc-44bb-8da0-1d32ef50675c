# api_client.api.GoogleApi

## Load the API package
```dart
import 'package:api_client/api.dart';
```

All URIs are relative to *http://localhost*

Method | HTTP request | Description
------------- | ------------- | -------------
[**googleGoogleCallback**](GoogleApi.md#googlegooglecallback) | **GET** /api/v1/google/callback | Google Callback
[**googleGoogleLogin**](GoogleApi.md#googlegooglelogin) | **GET** /api/v1/google/login | Google Login


# **googleGoogleCallback**
> JsonObject googleGoogleCallback()

Google Callback

Handle Google OAuth callback and login/create user

### Example
```dart
import 'package:api_client/api.dart';

final api = ApiClient().getGoogleApi();

try {
    final response = api.googleGoogleCallback();
    print(response);
} catch on DioException (e) {
    print('Exception when calling GoogleApi->googleGoogleCallback: $e\n');
}
```

### Parameters
This endpoint does not need any parameter.

### Return type

[**JsonObject**](JsonObject.md)

### Authorization

No authorization required

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: application/json

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **googleGoogleLogin**
> JsonObject googleGoogleLogin()

Google Login

Initiate Google OAuth login flow

### Example
```dart
import 'package:api_client/api.dart';

final api = ApiClient().getGoogleApi();

try {
    final response = api.googleGoogleLogin();
    print(response);
} catch on DioException (e) {
    print('Exception when calling GoogleApi->googleGoogleLogin: $e\n');
}
```

### Parameters
This endpoint does not need any parameter.

### Return type

[**JsonObject**](JsonObject.md)

### Authorization

No authorization required

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: application/json

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

