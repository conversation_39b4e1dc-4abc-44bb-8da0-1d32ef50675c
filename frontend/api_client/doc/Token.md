# api_client.model.Token

## Load the model package
```dart
import 'package:api_client/api.dart';
```

## Properties
Name | Type | Description | Notes
------------ | ------------- | ------------- | -------------
**accessToken** | **String** |  | 
**refreshToken** | **String** |  | [optional] 
**tokenType** | **String** |  | [optional] [default to 'bearer']
**isFirstLogin** | **bool** |  | [optional] [default to false]
**expiresIn** | **int** |  | [optional] [default to 3600]

[[Back to Model list]](../README.md#documentation-for-models) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to README]](../README.md)


