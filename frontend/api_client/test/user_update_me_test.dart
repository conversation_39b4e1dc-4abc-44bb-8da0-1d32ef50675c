import 'package:test/test.dart';
import 'package:api_client/api_client.dart';

// tests for UserUpdateMe
void main() {
  final instance = UserUpdateMeBuilder();
  // TODO add properties to the builder and call build()

  group(UserUpdateMe, () {
    // String fullName
    test('to test the property `fullName`', () async {
      // TODO
    });

    // String email
    test('to test the property `email`', () async {
      // TODO
    });
  });
}
