import 'package:test/test.dart';
import 'package:api_client/api_client.dart';

/// tests for LoginApi
void main() {
  final instance = ApiClient().getLoginApi();

  group(LoginApi, () {
    // Login Access Token
    //
    // OAuth2 compatible token login, get an access token for future requests
    //
    //Future<Token> loginLoginAccessToken(String username, String password, { String grantType, String scope, String clientId, String clientSecret }) async
    test('test loginLoginAccessToken', () async {
      // TODO
    });

    // Test Token
    //
    // Test access token
    //
    //Future<UserPublic> loginTestToken() async
    test('test loginTestToken', () async {
      // TODO
    });
  });
}
