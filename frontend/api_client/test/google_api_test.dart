import 'package:test/test.dart';
import 'package:api_client/api_client.dart';

/// tests for GoogleApi
void main() {
  final instance = ApiClient().getGoogleApi();

  group(GoogleApi, () {
    // Google Callback
    //
    // Handle Google OAuth callback and login/create user
    //
    //Future<JsonObject> googleGoogleCallback() async
    test('test googleGoogleCallback', () async {
      // TODO
    });

    // Google Login
    //
    // Initiate Google OAuth login flow
    //
    //Future<JsonObject> googleGoogleLogin() async
    test('test googleGoogleLogin', () async {
      // TODO
    });
  });
}
