import 'package:test/test.dart';
import 'package:api_client/api_client.dart';

// tests for UpdatePassword
void main() {
  final instance = UpdatePasswordBuilder();
  // TODO add properties to the builder and call build()

  group(UpdatePassword, () {
    // String currentPassword
    test('to test the property `currentPassword`', () async {
      // TODO
    });

    // String newPassword
    test('to test the property `newPassword`', () async {
      // TODO
    });
  });
}
