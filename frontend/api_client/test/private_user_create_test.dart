import 'package:test/test.dart';
import 'package:api_client/api_client.dart';

// tests for PrivateUserCreate
void main() {
  final instance = PrivateUserCreateBuilder();
  // TODO add properties to the builder and call build()

  group(PrivateUserCreate, () {
    // String email
    test('to test the property `email`', () async {
      // TODO
    });

    // String password
    test('to test the property `password`', () async {
      // TODO
    });

    // String fullName
    test('to test the property `fullName`', () async {
      // TODO
    });

    // bool isVerified (default value: false)
    test('to test the property `isVerified`', () async {
      // TODO
    });
  });
}
