import 'package:test/test.dart';
import 'package:api_client/api_client.dart';

// tests for UserRegister
void main() {
  final instance = UserRegisterBuilder();
  // TODO add properties to the builder and call build()

  group(UserRegister, () {
    // String email
    test('to test the property `email`', () async {
      // TODO
    });

    // String password
    test('to test the property `password`', () async {
      // TODO
    });

    // String fullName
    test('to test the property `fullName`', () async {
      // TODO
    });
  });
}
