import 'package:test/test.dart';
import 'package:api_client/api_client.dart';

// tests for UsersPublic
void main() {
  final instance = UsersPublicBuilder();
  // TODO add properties to the builder and call build()

  group(UsersPublic, () {
    // BuiltList<UserPublic> data
    test('to test the property `data`', () async {
      // TODO
    });

    // int count
    test('to test the property `count`', () async {
      // TODO
    });
  });
}
