import 'package:test/test.dart';
import 'package:api_client/api_client.dart';

/// tests for UtilsApi
void main() {
  final instance = ApiClient().getUtilsApi();

  group(UtilsApi, () {
    // Health Check
    //
    //Future<bool> utilsHealthCheck() async
    test('test utilsHealthCheck', () async {
      // TODO
    });

    // Health Check
    //
    //Future<bool> utilsHealthCheck_0() async
    test('test utilsHealthCheck_0', () async {
      // TODO
    });

    // Test Email
    //
    // Test emails.
    //
    //Future<Message> utilsTestEmail(String emailTo) async
    test('test utilsTestEmail', () async {
      // TODO
    });

    // Test Email
    //
    // Test emails.
    //
    //Future<Message> utilsTestEmail_0(String emailTo) async
    test('test utilsTestEmail_0', () async {
      // TODO
    });
  });
}
