import 'package:test/test.dart';
import 'package:api_client/api_client.dart';

/// tests for AgentApi
void main() {
  final instance = ApiClient().getAgentApi();

  group(AgentApi, () {
    // Chat With Agent
    //
    // Chat with the YouRAI agent with multimodal input support (text + optional images)
    //
    //Future<JsonObject> agentChatWithAgent(String message, { String sessionId, BuiltList<MultipartFile> images }) async
    test('test agentChatWithAgent', () async {
      // TODO
    });

    // Get Conversation History
    //
    // Get the history of a specific conversation session
    //
    //Future<Conversation> agentGetConversationHistory(String sessionId) async
    test('test agentGetConversationHistory', () async {
      // TODO
    });

    // Get Conversations
    //
    // Get all conversation session IDs for the current user
    //
    //Future<BuiltList<String>> agentGetConversations() async
    test('test agentGetConversations', () async {
      // TODO
    });
  });
}
