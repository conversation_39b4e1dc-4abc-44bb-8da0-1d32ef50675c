import 'package:test/test.dart';
import 'package:api_client/api_client.dart';

// tests for Conversation
void main() {
  final instance = ConversationBuilder();
  // TODO add properties to the builder and call build()

  group(Conversation, () {
    // String sessionId
    test('to test the property `sessionId`', () async {
      // TODO
    });

    // BuiltList<JsonObject> history
    test('to test the property `history`', () async {
      // TODO
    });
  });
}
