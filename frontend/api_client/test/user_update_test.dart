import 'package:test/test.dart';
import 'package:api_client/api_client.dart';

// tests for UserUpdate
void main() {
  final instance = UserUpdateBuilder();
  // TODO add properties to the builder and call build()

  group(UserUpdate, () {
    // String email
    test('to test the property `email`', () async {
      // TODO
    });

    // bool isActive (default value: true)
    test('to test the property `isActive`', () async {
      // TODO
    });

    // bool isSuperuser (default value: false)
    test('to test the property `isSuperuser`', () async {
      // TODO
    });

    // String fullName
    test('to test the property `fullName`', () async {
      // TODO
    });

    // String avatarUrl
    test('to test the property `avatarUrl`', () async {
      // TODO
    });

    // DateTime lastLoginTime
    test('to test the property `lastLoginTime`', () async {
      // TODO
    });

    // String password
    test('to test the property `password`', () async {
      // TODO
    });
  });
}
