import 'package:test/test.dart';
import 'package:api_client/api_client.dart';

// tests for Token
void main() {
  final instance = TokenBuilder();
  // TODO add properties to the builder and call build()

  group(Token, () {
    // String accessToken
    test('to test the property `accessToken`', () async {
      // TODO
    });

    // String refreshToken
    test('to test the property `refreshToken`', () async {
      // TODO
    });

    // String tokenType (default value: 'bearer')
    test('to test the property `tokenType`', () async {
      // TODO
    });

    // bool isFirstLogin (default value: false)
    test('to test the property `isFirstLogin`', () async {
      // TODO
    });

    // int expiresIn (default value: 3600)
    test('to test the property `expiresIn`', () async {
      // TODO
    });
  });
}
