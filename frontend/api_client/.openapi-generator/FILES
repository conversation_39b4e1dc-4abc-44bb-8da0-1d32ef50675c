.gitignore
.openapi-generator-ignore
README.md
analysis_options.yaml
doc/AgentApi.md
doc/Conversation.md
doc/GoogleApi.md
doc/HTTPValidationError.md
doc/LoginApi.md
doc/Message.md
doc/PrivateApi.md
doc/PrivateUserCreate.md
doc/Token.md
doc/UpdatePassword.md
doc/UserCreate.md
doc/UserPublic.md
doc/UserRegister.md
doc/UserUpdate.md
doc/UserUpdateMe.md
doc/UsersApi.md
doc/UsersPublic.md
doc/UtilsApi.md
doc/ValidationError.md
doc/ValidationErrorLocInner.md
lib/api_client.dart
lib/src/api.dart
lib/src/api/agent_api.dart
lib/src/api/google_api.dart
lib/src/api/login_api.dart
lib/src/api/private_api.dart
lib/src/api/users_api.dart
lib/src/api/utils_api.dart
lib/src/api_util.dart
lib/src/auth/api_key_auth.dart
lib/src/auth/auth.dart
lib/src/auth/basic_auth.dart
lib/src/auth/bearer_auth.dart
lib/src/auth/oauth.dart
lib/src/date_serializer.dart
lib/src/model/conversation.dart
lib/src/model/date.dart
lib/src/model/http_validation_error.dart
lib/src/model/message.dart
lib/src/model/private_user_create.dart
lib/src/model/token.dart
lib/src/model/update_password.dart
lib/src/model/user_create.dart
lib/src/model/user_public.dart
lib/src/model/user_register.dart
lib/src/model/user_update.dart
lib/src/model/user_update_me.dart
lib/src/model/users_public.dart
lib/src/model/validation_error.dart
lib/src/model/validation_error_loc_inner.dart
lib/src/serializers.dart
pubspec.yaml
test/agent_api_test.dart
test/conversation_test.dart
test/google_api_test.dart
test/http_validation_error_test.dart
test/login_api_test.dart
test/message_test.dart
test/private_api_test.dart
test/private_user_create_test.dart
test/token_test.dart
test/update_password_test.dart
test/user_create_test.dart
test/user_public_test.dart
test/user_register_test.dart
test/user_update_me_test.dart
test/user_update_test.dart
test/users_api_test.dart
test/users_public_test.dart
test/utils_api_test.dart
test/validation_error_loc_inner_test.dart
test/validation_error_test.dart
