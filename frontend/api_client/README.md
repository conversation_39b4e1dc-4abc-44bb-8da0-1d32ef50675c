# api_client (EXPERIMENTAL)
No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)

This Dart package is automatically generated by the [OpenAPI Generator](https://openapi-generator.tech) project:

- API version: 0.1.0
- Generator version: 7.9.0
- Build package: org.openapitools.codegen.languages.DartDioClientCodegen

## Requirements

* Dart 2.15.0+ or Flutter 2.8.0+
* Dio 5.0.0+ (https://pub.dev/packages/dio)

## Installation & Usage

### pub.dev
To use the package from [pub.dev](https://pub.dev), please include the following in pubspec.yaml
```yaml
dependencies:
  api_client: 1.0.0
```

### Github
If this Dart package is published to Github, please include the following in pubspec.yaml
```yaml
dependencies:
  api_client:
    git:
      url: https://github.com/GIT_USER_ID/GIT_REPO_ID.git
      #ref: main
```

### Local development
To use the package from your local drive, please include the following in pubspec.yaml
```yaml
dependencies:
  api_client:
    path: /path/to/api_client
```

## Getting Started

Please follow the [installation procedure](#installation--usage) and then run the following:

```dart
import 'package:api_client/api_client.dart';


final api = ApiClient().getAgentApi();
final String message = message_example; // String | 
final String sessionId = sessionId_example; // String | 
final BuiltList<MultipartFile> images = /path/to/file.txt; // BuiltList<MultipartFile> | 

try {
    final response = await api.agentChatWithAgent(message, sessionId, images);
    print(response);
} catch on DioException (e) {
    print("Exception when calling AgentApi->agentChatWithAgent: $e\n");
}

```

## Documentation for API Endpoints

All URIs are relative to *http://localhost*

Class | Method | HTTP request | Description
------------ | ------------- | ------------- | -------------
[*AgentApi*](doc/AgentApi.md) | [**agentChatWithAgent**](doc/AgentApi.md#agentchatwithagent) | **POST** /api/v1/agent/chat | Chat With Agent
[*AgentApi*](doc/AgentApi.md) | [**agentGetConversationHistory**](doc/AgentApi.md#agentgetconversationhistory) | **GET** /api/v1/agent/conversations/{session_id} | Get Conversation History
[*AgentApi*](doc/AgentApi.md) | [**agentGetConversations**](doc/AgentApi.md#agentgetconversations) | **GET** /api/v1/agent/conversations | Get Conversations
[*GoogleApi*](doc/GoogleApi.md) | [**googleGoogleCallback**](doc/GoogleApi.md#googlegooglecallback) | **GET** /api/v1/google/callback | Google Callback
[*GoogleApi*](doc/GoogleApi.md) | [**googleGoogleLogin**](doc/GoogleApi.md#googlegooglelogin) | **GET** /api/v1/google/login | Google Login
[*LoginApi*](doc/LoginApi.md) | [**loginLoginAccessToken**](doc/LoginApi.md#loginloginaccesstoken) | **POST** /api/v1/login/access-token | Login Access Token
[*LoginApi*](doc/LoginApi.md) | [**loginTestToken**](doc/LoginApi.md#logintesttoken) | **POST** /api/v1/login/test-token | Test Token
[*PrivateApi*](doc/PrivateApi.md) | [**privateCreateUser**](doc/PrivateApi.md#privatecreateuser) | **POST** /api/v1/private/users/ | Create User
[*UsersApi*](doc/UsersApi.md) | [**usersCreateUser**](doc/UsersApi.md#userscreateuser) | **POST** /api/v1/users/ | Create User
[*UsersApi*](doc/UsersApi.md) | [**usersDeleteUser**](doc/UsersApi.md#usersdeleteuser) | **DELETE** /api/v1/users/{user_id} | Delete User
[*UsersApi*](doc/UsersApi.md) | [**usersDeleteUserMe**](doc/UsersApi.md#usersdeleteuserme) | **DELETE** /api/v1/users/me | Delete User Me
[*UsersApi*](doc/UsersApi.md) | [**usersReadUserById**](doc/UsersApi.md#usersreaduserbyid) | **GET** /api/v1/users/{user_id} | Read User By Id
[*UsersApi*](doc/UsersApi.md) | [**usersReadUserMe**](doc/UsersApi.md#usersreaduserme) | **GET** /api/v1/users/me | Read User Me
[*UsersApi*](doc/UsersApi.md) | [**usersReadUsers**](doc/UsersApi.md#usersreadusers) | **GET** /api/v1/users/ | Read Users
[*UsersApi*](doc/UsersApi.md) | [**usersRegisterUser**](doc/UsersApi.md#usersregisteruser) | **POST** /api/v1/users/signup | Register User
[*UsersApi*](doc/UsersApi.md) | [**usersUpdatePasswordMe**](doc/UsersApi.md#usersupdatepasswordme) | **PATCH** /api/v1/users/me/password | Update Password Me
[*UsersApi*](doc/UsersApi.md) | [**usersUpdateUser**](doc/UsersApi.md#usersupdateuser) | **PATCH** /api/v1/users/{user_id} | Update User
[*UsersApi*](doc/UsersApi.md) | [**usersUpdateUserMe**](doc/UsersApi.md#usersupdateuserme) | **PATCH** /api/v1/users/me | Update User Me
[*UtilsApi*](doc/UtilsApi.md) | [**utilsHealthCheck**](doc/UtilsApi.md#utilshealthcheck) | **GET** /api/v1/utils/utils/health-check/ | Health Check
[*UtilsApi*](doc/UtilsApi.md) | [**utilsHealthCheck_0**](doc/UtilsApi.md#utilshealthcheck_0) | **GET** /api/v1/utils/utils/health-check/ | Health Check
[*UtilsApi*](doc/UtilsApi.md) | [**utilsTestEmail**](doc/UtilsApi.md#utilstestemail) | **POST** /api/v1/utils/utils/test-email/ | Test Email
[*UtilsApi*](doc/UtilsApi.md) | [**utilsTestEmail_0**](doc/UtilsApi.md#utilstestemail_0) | **POST** /api/v1/utils/utils/test-email/ | Test Email


## Documentation For Models

 - [Conversation](doc/Conversation.md)
 - [HTTPValidationError](doc/HTTPValidationError.md)
 - [Message](doc/Message.md)
 - [PrivateUserCreate](doc/PrivateUserCreate.md)
 - [Token](doc/Token.md)
 - [UpdatePassword](doc/UpdatePassword.md)
 - [UserCreate](doc/UserCreate.md)
 - [UserPublic](doc/UserPublic.md)
 - [UserRegister](doc/UserRegister.md)
 - [UserUpdate](doc/UserUpdate.md)
 - [UserUpdateMe](doc/UserUpdateMe.md)
 - [UsersPublic](doc/UsersPublic.md)
 - [ValidationError](doc/ValidationError.md)
 - [ValidationErrorLocInner](doc/ValidationErrorLocInner.md)


## Documentation For Authorization


Authentication schemes defined for the API:
### OAuth2PasswordBearer

- **Type**: OAuth
- **Flow**: password
- **Authorization URL**: 
- **Scopes**: N/A


## Author



