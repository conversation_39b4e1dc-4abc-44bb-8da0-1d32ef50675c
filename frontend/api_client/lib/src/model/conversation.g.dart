// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'conversation.dart';

// **************************************************************************
// BuiltValueGenerator
// **************************************************************************

class _$Conversation extends Conversation {
  @override
  final String sessionId;
  @override
  final BuiltList<JsonObject?> history;

  factory _$Conversation([void Function(ConversationBuilder)? updates]) =>
      (ConversationBuilder()..update(updates))._build();

  _$Conversation._({required this.sessionId, required this.history})
      : super._();
  @override
  Conversation rebuild(void Function(ConversationBuilder) updates) =>
      (toBuilder()..update(updates)).build();

  @override
  ConversationBuilder toBuilder() => ConversationBuilder()..replace(this);

  @override
  bool operator ==(Object other) {
    if (identical(other, this)) return true;
    return other is Conversation &&
        sessionId == other.sessionId &&
        history == other.history;
  }

  @override
  int get hashCode {
    var _$hash = 0;
    _$hash = $jc(_$hash, sessionId.hashCode);
    _$hash = $jc(_$hash, history.hashCode);
    _$hash = $jf(_$hash);
    return _$hash;
  }

  @override
  String toString() {
    return (newBuiltValueToStringHelper(r'Conversation')
          ..add('sessionId', sessionId)
          ..add('history', history))
        .toString();
  }
}

class ConversationBuilder
    implements Builder<Conversation, ConversationBuilder> {
  _$Conversation? _$v;

  String? _sessionId;
  String? get sessionId => _$this._sessionId;
  set sessionId(String? sessionId) => _$this._sessionId = sessionId;

  ListBuilder<JsonObject?>? _history;
  ListBuilder<JsonObject?> get history =>
      _$this._history ??= ListBuilder<JsonObject?>();
  set history(ListBuilder<JsonObject?>? history) => _$this._history = history;

  ConversationBuilder() {
    Conversation._defaults(this);
  }

  ConversationBuilder get _$this {
    final $v = _$v;
    if ($v != null) {
      _sessionId = $v.sessionId;
      _history = $v.history.toBuilder();
      _$v = null;
    }
    return this;
  }

  @override
  void replace(Conversation other) {
    _$v = other as _$Conversation;
  }

  @override
  void update(void Function(ConversationBuilder)? updates) {
    if (updates != null) updates(this);
  }

  @override
  Conversation build() => _build();

  _$Conversation _build() {
    _$Conversation _$result;
    try {
      _$result = _$v ??
          _$Conversation._(
            sessionId: BuiltValueNullFieldError.checkNotNull(
                sessionId, r'Conversation', 'sessionId'),
            history: history.build(),
          );
    } catch (_) {
      late String _$failedField;
      try {
        _$failedField = 'history';
        history.build();
      } catch (e) {
        throw BuiltValueNestedFieldError(
            r'Conversation', _$failedField, e.toString());
      }
      rethrow;
    }
    replace(_$result);
    return _$result;
  }
}

// ignore_for_file: deprecated_member_use_from_same_package,type=lint
