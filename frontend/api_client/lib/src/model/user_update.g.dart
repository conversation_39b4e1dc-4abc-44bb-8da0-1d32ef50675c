// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'user_update.dart';

// **************************************************************************
// BuiltValueGenerator
// **************************************************************************

class _$UserUpdate extends UserUpdate {
  @override
  final String? email;
  @override
  final bool? isActive;
  @override
  final bool? isSuperuser;
  @override
  final String? fullName;
  @override
  final String? avatarUrl;
  @override
  final DateTime? lastLoginTime;
  @override
  final String? password;

  factory _$UserUpdate([void Function(UserUpdateBuilder)? updates]) =>
      (UserUpdateBuilder()..update(updates))._build();

  _$UserUpdate._(
      {this.email,
      this.isActive,
      this.isSuperuser,
      this.fullName,
      this.avatarUrl,
      this.lastLoginTime,
      this.password})
      : super._();
  @override
  UserUpdate rebuild(void Function(UserUpdateBuilder) updates) =>
      (toBuilder()..update(updates)).build();

  @override
  UserUpdateBuilder toBuilder() => UserUpdateBuilder()..replace(this);

  @override
  bool operator ==(Object other) {
    if (identical(other, this)) return true;
    return other is UserUpdate &&
        email == other.email &&
        isActive == other.isActive &&
        isSuperuser == other.isSuperuser &&
        fullName == other.fullName &&
        avatarUrl == other.avatarUrl &&
        lastLoginTime == other.lastLoginTime &&
        password == other.password;
  }

  @override
  int get hashCode {
    var _$hash = 0;
    _$hash = $jc(_$hash, email.hashCode);
    _$hash = $jc(_$hash, isActive.hashCode);
    _$hash = $jc(_$hash, isSuperuser.hashCode);
    _$hash = $jc(_$hash, fullName.hashCode);
    _$hash = $jc(_$hash, avatarUrl.hashCode);
    _$hash = $jc(_$hash, lastLoginTime.hashCode);
    _$hash = $jc(_$hash, password.hashCode);
    _$hash = $jf(_$hash);
    return _$hash;
  }

  @override
  String toString() {
    return (newBuiltValueToStringHelper(r'UserUpdate')
          ..add('email', email)
          ..add('isActive', isActive)
          ..add('isSuperuser', isSuperuser)
          ..add('fullName', fullName)
          ..add('avatarUrl', avatarUrl)
          ..add('lastLoginTime', lastLoginTime)
          ..add('password', password))
        .toString();
  }
}

class UserUpdateBuilder implements Builder<UserUpdate, UserUpdateBuilder> {
  _$UserUpdate? _$v;

  String? _email;
  String? get email => _$this._email;
  set email(String? email) => _$this._email = email;

  bool? _isActive;
  bool? get isActive => _$this._isActive;
  set isActive(bool? isActive) => _$this._isActive = isActive;

  bool? _isSuperuser;
  bool? get isSuperuser => _$this._isSuperuser;
  set isSuperuser(bool? isSuperuser) => _$this._isSuperuser = isSuperuser;

  String? _fullName;
  String? get fullName => _$this._fullName;
  set fullName(String? fullName) => _$this._fullName = fullName;

  String? _avatarUrl;
  String? get avatarUrl => _$this._avatarUrl;
  set avatarUrl(String? avatarUrl) => _$this._avatarUrl = avatarUrl;

  DateTime? _lastLoginTime;
  DateTime? get lastLoginTime => _$this._lastLoginTime;
  set lastLoginTime(DateTime? lastLoginTime) =>
      _$this._lastLoginTime = lastLoginTime;

  String? _password;
  String? get password => _$this._password;
  set password(String? password) => _$this._password = password;

  UserUpdateBuilder() {
    UserUpdate._defaults(this);
  }

  UserUpdateBuilder get _$this {
    final $v = _$v;
    if ($v != null) {
      _email = $v.email;
      _isActive = $v.isActive;
      _isSuperuser = $v.isSuperuser;
      _fullName = $v.fullName;
      _avatarUrl = $v.avatarUrl;
      _lastLoginTime = $v.lastLoginTime;
      _password = $v.password;
      _$v = null;
    }
    return this;
  }

  @override
  void replace(UserUpdate other) {
    _$v = other as _$UserUpdate;
  }

  @override
  void update(void Function(UserUpdateBuilder)? updates) {
    if (updates != null) updates(this);
  }

  @override
  UserUpdate build() => _build();

  _$UserUpdate _build() {
    final _$result = _$v ??
        _$UserUpdate._(
          email: email,
          isActive: isActive,
          isSuperuser: isSuperuser,
          fullName: fullName,
          avatarUrl: avatarUrl,
          lastLoginTime: lastLoginTime,
          password: password,
        );
    replace(_$result);
    return _$result;
  }
}

// ignore_for_file: deprecated_member_use_from_same_package,type=lint
