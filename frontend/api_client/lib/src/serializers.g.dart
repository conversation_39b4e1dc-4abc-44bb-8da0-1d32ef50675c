// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'serializers.dart';

// **************************************************************************
// BuiltValueGenerator
// **************************************************************************

Serializers _$serializers = (Serializers().toBuilder()
      ..add(Conversation.serializer)
      ..add(HTTPValidationError.serializer)
      ..add(Message.serializer)
      ..add(PrivateUserCreate.serializer)
      ..add(Token.serializer)
      ..add(UpdatePassword.serializer)
      ..add(UserCreate.serializer)
      ..add(UserPublic.serializer)
      ..add(UserRegister.serializer)
      ..add(UserUpdate.serializer)
      ..add(UserUpdateMe.serializer)
      ..add(UsersPublic.serializer)
      ..add(ValidationError.serializer)
      ..add(ValidationErrorLocInner.serializer)
      ..addBuilderFactory(
          const FullType(BuiltList, const [const FullType(UserPublic)]),
          () => ListBuilder<UserPublic>())
      ..addBuilderFactory(
          const FullType(BuiltList, const [const FullType(ValidationError)]),
          () => ListBuilder<ValidationError>())
      ..addBuilderFactory(
          const FullType(
              BuiltList, const [const FullType(ValidationErrorLocInner)]),
          () => ListBuilder<ValidationErrorLocInner>())
      ..addBuilderFactory(
          const FullType(
              BuiltList, const [const FullType.nullable(JsonObject)]),
          () => ListBuilder<JsonObject?>()))
    .build();

// ignore_for_file: deprecated_member_use_from_same_package,type=lint
