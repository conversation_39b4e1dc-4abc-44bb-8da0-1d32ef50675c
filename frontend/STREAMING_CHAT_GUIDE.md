# Streaming Chat Implementation Guide

## Overview

This guide covers the enhanced streaming chat implementation that provides real-time, responsive chat interactions with the YouRAI backend. The implementation supports text streaming, tool calls, image uploads, and robust error handling.

## Architecture

### Components

1. **StreamingChatService** - Core service for handling streaming chat requests
2. **ChatEvent** - Type-safe event system for streaming responses
3. **Enhanced ChatScreen** - Updated UI with improved streaming support
4. **API Client Integration** - Seamless integration with generated API client

### Event Types

```dart
enum ChatEventType {
  sessionId,    // New session ID from server
  textChunk,    // Incremental text content
  toolStart,    // Tool execution started
  toolEnd,      // Tool execution completed
  error,        // Error occurred
  done,         // Stream completed
}
```

## Usage Examples

### Basic Text Streaming

```dart
import 'package:best_flutter_ui_templates/chat/streaming_chat_service.dart';

final chatService = StreamingChatService.instance;

// Send a simple message
final stream = await chatService.sendTextMessage(
  'Hello! How are you today?',
  sessionId: 'optional-session-id',
);

// Listen to streaming events
await for (final event in stream) {
  switch (event.type) {
    case ChatEventType.sessionId:
      print('Session: ${event.data}');
      break;
    case ChatEventType.textChunk:
      print('Text: ${event.data}');
      break;
    case ChatEventType.done:
      print('Stream completed');
      break;
    case ChatEventType.error:
      print('Error: ${event.data}');
      break;
  }
}
```

### Streaming with Images

```dart
import 'dart:io';

final images = [File('/path/to/image1.jpg'), File('/path/to/image2.png')];

final stream = await chatService.sendMessageWithImages(
  'What do you see in these images?',
  images,
  sessionId: sessionId,
);

// Handle events the same way as text streaming
```

### Tool Call Handling

```dart
await for (final event in stream) {
  switch (event.type) {
    case ChatEventType.toolStart:
      final toolData = event.data as Map<String, dynamic>;
      print('Tool ${toolData['name']} started with args: ${toolData['args']}');
      break;
      
    case ChatEventType.toolEnd:
      final toolData = event.data as Map<String, dynamic>;
      print('Tool ${toolData['name']} completed: ${toolData['output']}');
      break;
  }
}
```

## Integration with Chat Screen

The enhanced chat screen automatically handles all streaming events:

```dart
class _ChatScreenState extends State<ChatScreen> {
  final StreamingChatService _chatService = StreamingChatService.instance;
  StreamSubscription<ChatEvent>? _streamSubscription;

  void _handleSendMessage(String text) async {
    // Cancel any existing stream
    await _streamSubscription?.cancel();
    
    // Add user message to UI
    setState(() {
      _messages.insert(0, ChatMessage(text: text, isUserMessage: true));
      _isBotTyping = true;
    });

    // Start streaming
    final stream = await _chatService.sendTextMessage(text, sessionId: sessionId);
    
    _streamSubscription = stream.listen(
      (event) => _handleChatEvent(event),
      onDone: () => setState(() => _isBotTyping = false),
      onError: (error) => _showErrorMessage('Stream error: $error'),
    );
  }
}
```

## Error Handling

The streaming service includes comprehensive error handling:

1. **Network Errors** - Automatic retry and graceful degradation
2. **Parse Errors** - Robust JSON parsing with fallback error events
3. **Stream Interruption** - Clean cancellation and resource cleanup
4. **Authentication Errors** - Proper error propagation to UI

## Testing

Use the provided test utilities to verify streaming functionality:

```dart
import 'package:best_flutter_ui_templates/chat/streaming_test.dart';

// Run all streaming tests
await StreamingTest.runAllTests();

// Test specific functionality
await StreamingTest.testStreamingChat();
await StreamingTest.testErrorHandling();
```

## Performance Considerations

1. **Memory Management** - Streams are properly disposed to prevent memory leaks
2. **Cancellation** - Previous streams are cancelled when new messages are sent
3. **Buffering** - Text chunks are efficiently concatenated for smooth display
4. **Error Recovery** - Failed streams don't crash the application

## Backend Compatibility

This implementation is compatible with the FastAPI backend that returns Server-Sent Events (SSE) in the format:

```
data: {"type": "session_id", "data": "session-123"}
data: {"type": "text_chunk", "data": "Hello"}
data: {"type": "text_chunk", "data": " world!"}
data: {"type": "done", "data": null}
```

## Migration from Old Implementation

If you're migrating from the old chat implementation:

1. Replace `ChatService` with `StreamingChatService`
2. Update event handling from `Map<String, dynamic>` to `ChatEvent`
3. Use the new event types instead of string matching
4. Update error handling to use the new error event system

## Troubleshooting

### Common Issues

1. **Stream Not Starting** - Check API client initialization
2. **Events Not Received** - Verify backend SSE format
3. **Memory Leaks** - Ensure stream subscriptions are cancelled
4. **UI Not Updating** - Check setState calls in event handlers

### Debug Mode

Enable debug logging to see detailed streaming information:

```dart
import 'package:flutter/foundation.dart';

if (kDebugMode) {
  // Debug logs will show streaming events and errors
}
```
