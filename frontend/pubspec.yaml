name: best_flutter_ui_templates
description: A new Flutter project.

# The following line prevents the package from being accidentally published to
# pub.dev using `pub publish`. This is preferred for private packages.
publish_to: "none"
# Remove this line if you wish to publish to pub.dev

# The following defines the version and build number for your application.
# A version number is three numbers separated by dots, like 1.2.43
# followed by an optional build number separated by a +.
# Both the version and the builder number may be overridden in flutter
# build by specifying --build-name and --build-number, respectively.
# In Android, build-name is used as versionName while build-number used as versionCode.
# Read more about Android versioning at https://developer.android.com/studio/publish/versioning
# In iOS, build-name is used as CFBundleShortVersionString while build-number used as CFBundleVersion.
# Read more about iOS versioning at
# https://developer.apple.com/library/archive/documentation/General/Reference/InfoPlistKeyReference/Articles/CoreFoundationKeys.html
version: 1.0.0+1

environment:
    sdk: ">=3.0.0 <4.0.0"

dependencies:
    flutter:
        sdk: flutter
    api_client:
        path: ./api_client
    google_sign_in: ^7.1.0
    url_launcher: ^6.3.0

    # The following adds the Cupertino Icons font to your application.
    # Use with the CupertinoIcons class for iOS style icons.
    cupertino_icons: ^1.0.8
    font_awesome_flutter: ^10.8.0
    flutter_rating_bar: ^4.0.1
    intl: ^0.20.2
    animations: ^2.0.11

    dio: ^5.8.0+1
    provider: ^6.1.5
    flutter_svg: ^2.2.0
    flutter_spinkit: ^5.2.1
    flutter_slidable: ^4.0.0
    awesome_snackbar_content: ^0.1.6
    cached_network_image: ^3.4.1
    logger: ^2.6.0
    camera: ^0.11.1
    flutter_markdown: ^0.7.1
    image_picker: ^1.0.7
    uuid: ^4.4.0
    path_provider: ^2.1.5
    shared_preferences: ^2.5.3
    openapi_generator_annotations: ^6.1.0

dev_dependencies:
    flutter_lints: ^6.0.0
    flutter_test:
        sdk: flutter
    openapi_generator: ^6.1.0
    build_runner: ^2.1.10

# For information on the generic Dart part of this file, see the
# following page: https://dart.dev/tools/pub/pubspec

# The following section is specific to Flutter.
flutter:
    # The following line ensures that the Material Icons font is
    # included with your application, so that you can use the icons in
    # the material Icons class.
    uses-material-design: true

    # To add assets to your application, add an assets section, like this:
    assets:
        - assets/images/
        - assets/yourai/

    # An image asset can refer to one or more resolution-specific "variants", see
    # https://flutter.dev/assets-and-images/#resolution-aware.

    # For details regarding adding assets from package dependencies, see
    # https://flutter.dev/assets-and-images/#from-packages

    # To add custom fonts to your application, add a fonts section here,
    # in this "flutter" section. Each entry in this list should have a
    # "family" key with the font family name, and a "fonts" key with a
    # list giving the asset and other descriptors for the font. For
    # example:
    fonts:
        - family: WorkSans
          fonts:
              - asset: assets/fonts/WorkSans-Regular.ttf
              - asset: assets/fonts/WorkSans-Medium.ttf
                weight: 500
              - asset: assets/fonts/WorkSans-SemiBold.ttf
                weight: 600
              - asset: assets/fonts/WorkSans-Bold.ttf
                weight: 700
        - family: Roboto
          fonts:
              - asset: assets/fonts/Roboto-Bold.ttf
              - asset: assets/fonts/Roboto-Regular.ttf
              - asset: assets/fonts/Roboto-Medium.ttf
                weight: 400
    #
    # For details regarding fonts from package dependencies,
    # see https://flutter.dev/custom-fonts/#from-packages
