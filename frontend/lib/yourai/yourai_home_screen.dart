import 'package:best_flutter_ui_templates/chat/chat_screen.dart';
import 'package:best_flutter_ui_templates/components/models/tab_icon_data.dart';
import 'package:flutter/material.dart';
import 'package:best_flutter_ui_templates/components/bottom_navigation/bottom_bar_view.dart';
import 'yourai_theme.dart';
import 'package:best_flutter_ui_templates/yourai/user_profile_screen.dart';
import 'package:best_flutter_ui_templates/yourai/empty_screen.dart';

class YourAIHomeScreen extends StatefulWidget {
  @override
  _YourAIHomeScreenState createState() => _YourAIHomeScreenState();
}

class _YourAIHomeScreenState extends State<YourAIHomeScreen> {
  List<TabIconData> tabIconsList = TabIconData.tabIconsList;
  late final List<Widget> _screens;
  int _currentIndex = 0;

  @override
  void initState() {
    super.initState();
    tabIconsList.forEach((TabIconData tab) {
      tab.isSelected = false;
    });
    tabIconsList[0].isSelected = true;

    _screens = [
      EmptyScreen(tabIndex: 0),
      ChatScreen(),
      EmptyScreen(tabIndex: 2),
      UserProfileScreen(),
    ];
  }

  @override
  void dispose() {
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      color: YourAITheme.background,
      child: Scaffold(
        backgroundColor: Colors.transparent,
        body: FutureBuilder<bool>(
          future: getData(),
          builder: (BuildContext context, AsyncSnapshot<bool> snapshot) {
            if (!snapshot.hasData) {
              return const SizedBox();
            } else {
              return Stack(
                children: <Widget>[
                  IndexedStack(
                    index: _currentIndex,
                    children: _screens,
                  ),
                  bottomBar(),
                ],
              );
            }
          },
        ),
      ),
    );
  }

  Future<bool> getData() async {
    await Future<dynamic>.delayed(const Duration(milliseconds: 200));
    return true;
  }

  Widget bottomBar() {
    return Column(
      children: <Widget>[
        const Expanded(
          child: SizedBox(),
        ),
        BottomBarView(
          tabIconsList: tabIconsList,
          addClick: () {},
          changeIndex: (int index) {
            if (!mounted) {
              return;
            }
            setState(() {
              _currentIndex = index;
            });
          },
        ),
      ],
    );
  }
}
