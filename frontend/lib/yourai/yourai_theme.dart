import 'package:flutter/material.dart';

class YourAITheme {
  YourAITheme._();
  static const Color darkSurface = Color(0xFF181A20);
  static const Color surface = Color(0xFF23242B);
  static const Color background = Color(0xFF101014);
  static const Color glassBackground = Color(0xAA23242B);
  static const Color nearlyDarkBlue = Color(0xFF375FFF);

  static const Color nearlyBlue = Color(0xFF00B6F0);
  static const Color pinkAccent = Color(0xFFFF3B7A);
  static const Color greenAccent = Color(0xFF00E6A2);
  static const Color yellowAccent = Color(0xFFFFD600);
  static const Color lightGray = Color(0xFFE0E0E0);
  static const Color grey = Color(0xFF7A7A7A);
  static const Color dark_grey = Color(0xFF23242B);

  static const Color darkText = Color(0xFFBFC7D5);
  static const Color darkerText = Color(0xFFF5F6FA);
  static const Color lightText = Color(0xFF8A8D98);
  static const Color deactivatedText = Color(0xFF5A5A5A);
  static const Color dismissibleBackground = Color(0xFF23242B);
  static const Color spacer = Color(0xFF181A20);
  static const String fontName = 'Roboto';

  static const TextTheme textTheme = TextTheme(
    headlineMedium: display1,  // headline4 -> headlineMedium
    headlineSmall: headline,   // headline5 -> headlineSmall
    titleLarge: title,         // headline6 -> titleLarge
    titleMedium: subtitle,     // subtitle2 -> titleMedium
    bodyMedium: body2,         // bodyText2 -> bodyMedium
    bodyLarge: body1,          // bodyText1 -> bodyLarge
    bodySmall: caption,        // caption -> bodySmall
  );

  static const TextStyle display1 = TextStyle(
    fontFamily: fontName,
    fontWeight: FontWeight.bold,
    fontSize: 36,
    letterSpacing: 0.4,
    height: 0.9,
    color: darkerText,
  );

  static const TextStyle headline = TextStyle(
    fontFamily: fontName,
    fontWeight: FontWeight.bold,
    fontSize: 24,
    letterSpacing: 0.27,
    color: darkerText,
  );

  static const TextStyle title = TextStyle(
    fontFamily: fontName,
    fontWeight: FontWeight.bold,
    fontSize: 16,
    letterSpacing: 0.18,
    color: darkerText,
  );

  static const TextStyle subtitle = TextStyle(
    fontFamily: fontName,
    fontWeight: FontWeight.w400,
    fontSize: 14,
    letterSpacing: -0.04,
    color: darkText,
  );

  static const TextStyle body2 = TextStyle(
    fontFamily: fontName,
    fontWeight: FontWeight.w400,
    fontSize: 14,
    letterSpacing: 0.2,
    color: darkText,
  );

  static const TextStyle body1 = TextStyle(
    fontFamily: fontName,
    fontWeight: FontWeight.w400,
    fontSize: 16,
    letterSpacing: -0.05,
    color: darkText,
  );

  static const TextStyle caption = TextStyle(
    fontFamily: fontName,
    fontWeight: FontWeight.w400,
    fontSize: 12,
    letterSpacing: 0.2,
    color: lightText, // was lightText
  );
}
