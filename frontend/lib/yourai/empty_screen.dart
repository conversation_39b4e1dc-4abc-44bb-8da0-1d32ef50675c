import 'package:flutter/material.dart';
import 'yourai_theme.dart';

class EmptyScreen extends StatelessWidget {
  final int tabIndex;
  const EmptyScreen({Key? key, required this.tabIndex}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: YourAITheme.background,
      body: Center(
        child: Text(
          'Tab ${tabIndex + 1} is coming soon!',
          style: TextStyle(
            fontFamily: YourAITheme.fontName,
            fontSize: 24,
            color: YourAITheme.darkerText,
            fontWeight: FontWeight.bold,
          ),
        ),
      ),
    );
  }
}