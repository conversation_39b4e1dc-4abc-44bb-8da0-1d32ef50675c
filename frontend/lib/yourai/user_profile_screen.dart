import 'dart:ui';
import 'package:flutter/material.dart';
import 'package:openapi/openapi.dart';
import 'package:provider/provider.dart';
import 'package:best_flutter_ui_templates/utils/login_status.dart';
import 'package:best_flutter_ui_templates/yourai/yourai_theme.dart';
import 'package:best_flutter_ui_templates/auth/auth_screen.dart';
import 'package:best_flutter_ui_templates/components/app_header/app_header.dart';

class UserProfileScreen extends StatefulWidget {
  const UserProfileScreen({Key? key}) : super(key: key);

  @override
  State<UserProfileScreen> createState() => _UserProfileScreenState();
}

class _UserProfileScreenState extends State<UserProfileScreen>
    with TickerProviderStateMixin {
  late final Openapi _openapi;
  late final UsersApi _usersApi;
  Future<UserPublic?>? _userFuture;
  late final AnimationController animationController;

  final ScrollController scrollController = ScrollController();

  @override
  void initState() {
    super.initState();
    _openapi = Openapi();
    _usersApi = _openapi.getUsersApi();
    _userFuture = _fetchUser();

    animationController = AnimationController(
        duration: const Duration(milliseconds: 600), vsync: this);
    animationController.forward();
  }

  @override
  void dispose() {
    animationController.dispose();
    super.dispose();
  }

  Future<UserPublic?> _fetchUser() async {
    try {
      final loginStatus = Provider.of<LoginStatus>(context, listen: false);
      if (loginStatus.accessToken != null) {
        // Set the access token for authenticated requests
        _openapi.setOAuthToken(
            'OAuth2PasswordBearer', loginStatus.accessToken!);

        final response = await _usersApi.usersReadUserMe();
        final user = response.data;
        if (user != null) {
          loginStatus.setUser(user);
        }
        return user;
      }
      return null;
    } catch (e) {
      return null;
    }
  }

  void _logout() async {
    // Show confirmation dialog
    final bool? confirmLogout = await showDialog<bool>(
      context: context,
      barrierDismissible: false,
      builder: (BuildContext context) {
        return AlertDialog(
          backgroundColor: YourAITheme.surface,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(16),
          ),
          title: Text(
            'Logout',
            style: TextStyle(
              fontFamily: YourAITheme.fontName,
              fontWeight: FontWeight.w700,
              fontSize: 20,
              color: YourAITheme.darkerText,
            ),
          ),
          content: Text(
            'Are you sure you want to logout?',
            style: TextStyle(
              fontFamily: YourAITheme.fontName,
              fontSize: 16,
              color: YourAITheme.darkText,
            ),
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(false),
              child: Text(
                'Cancel',
                style: TextStyle(
                  fontFamily: YourAITheme.fontName,
                  color: YourAITheme.darkText,
                ),
              ),
            ),
            ElevatedButton(
              onPressed: () => Navigator.of(context).pop(true),
              style: ElevatedButton.styleFrom(
                backgroundColor: YourAITheme.pinkAccent,
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(8),
                ),
              ),
              child: Text(
                'Logout',
                style: TextStyle(
                  fontFamily: YourAITheme.fontName,
                  color: Colors.white,
                ),
              ),
            ),
          ],
        );
      },
    );

    if (confirmLogout == true) {
      // Perform logout
      final loginStatus = Provider.of<LoginStatus>(context, listen: false);
      loginStatus.logout();

      // Navigate to auth screen
      Navigator.of(context).pushAndRemoveUntil(
        MaterialPageRoute(builder: (context) => const AuthScreen()),
        (Route<dynamic> route) => false,
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      color: YourAITheme.background,
      child: Scaffold(
        backgroundColor: Colors.transparent,
        body: Stack(
          children: <Widget>[
            getMainUI(),
            AppHeader(
              animationController: animationController,
              scrollController: scrollController,
              title: 'Profile',
              showDatePicker: false,
            ),
            SizedBox(
              height: MediaQuery.of(context).padding.bottom,
            )
          ],
        ),
      ),
    );
  }

  Widget getMainUI() {
    return FutureBuilder<UserPublic?>(
      future: _userFuture,
      builder: (context, snapshot) {
        if (snapshot.connectionState == ConnectionState.waiting) {
          return const Center(child: CircularProgressIndicator());
        } else if (snapshot.hasError || snapshot.data == null) {
          return Center(
            child: Text(
              'Failed to load user profile',
              style: TextStyle(
                fontFamily: YourAITheme.fontName,
                fontSize: 18,
                color: Colors.white,
              ),
            ),
          );
        }
        final user = snapshot.data!;
        return ListView(
          controller: scrollController,
          padding: EdgeInsets.only(
            top: AppBar().preferredSize.height +
                MediaQuery.of(context).padding.top +
                24,
            bottom: 62 + MediaQuery.of(context).padding.bottom,
          ),
          children: [
            Padding(
              padding: const EdgeInsets.all(24.0),
              child: Column(
                children: [
                  // Profile Avatar
                  SizedBox(
                    height: 120,
                    width: 120,
                    child: ClipOval(
                      child: BackdropFilter(
                        filter: ImageFilter.blur(sigmaX: 10, sigmaY: 10),
                        child: Container(
                          decoration: BoxDecoration(
                            shape: BoxShape.circle,
                            gradient: LinearGradient(
                              colors: [
                                Colors.white.withOpacity(0.2),
                                Colors.white.withOpacity(0.1),
                              ],
                            ),
                            border: Border.all(
                              color: Colors.white.withOpacity(0.3),
                              width: 1.5,
                            ),
                          ),
                          child: user.avatarUrl != null &&
                                  user.avatarUrl!.isNotEmpty
                              ? ClipOval(
                                  child: Image.network(
                                    user.avatarUrl!,
                                    fit: BoxFit.cover,
                                    errorBuilder:
                                        (context, error, stackTrace) => Icon(
                                      Icons.person,
                                      size: 60,
                                      color: Colors.white,
                                    ),
                                  ),
                                )
                              : Icon(
                                  Icons.person,
                                  size: 60,
                                  color: Colors.white,
                                ),
                        ),
                      ),
                    ),
                  ),
                  const SizedBox(height: 24),

                  // User Name
                  Text(
                    user.fullName ?? 'No Name',
                    style: TextStyle(
                      fontFamily: YourAITheme.fontName,
                      fontWeight: FontWeight.w700,
                      fontSize: 22,
                      letterSpacing: 1.2,
                      color: Colors.white,
                    ),
                  ),
                  const SizedBox(height: 8),

                  // Email
                  Text(
                    user.email,
                    style: TextStyle(
                      fontFamily: YourAITheme.fontName,
                      fontWeight: FontWeight.normal,
                      fontSize: 16,
                      letterSpacing: -0.2,
                      color: Colors.white,
                    ),
                  ),
                  const SizedBox(height: 32),

                  // Profile Info Cards
                  _buildInfoCard(
                    'Account Status',
                    user.isActive == true ? 'Active' : 'Inactive',
                    Icons.check_circle,
                    user.isActive == true ? Colors.green : Colors.red,
                  ),
                  const SizedBox(height: 16),

                  if (user.lastLoginTime != null)
                    _buildInfoCard(
                      'Last Login',
                      _formatDate(user.lastLoginTime!),
                      Icons.access_time,
                      YourAITheme.nearlyDarkBlue,
                    ),
                  const SizedBox(height: 16),

                  _buildInfoCard(
                    'User ID',
                    user.id,
                    Icons.fingerprint,
                    YourAITheme.grey,
                  ),
                  const SizedBox(height: 32),

                  // Logout Button
                  _buildLogoutButton(),
                ],
              ),
            ),
          ],
        );
      },
    );
  }

  Widget _buildLogoutButton() {
    return SizedBox(
      width: double.infinity,
      height: 56,
      child: ClipRRect(
        borderRadius: BorderRadius.circular(16),
        child: BackdropFilter(
          filter: ImageFilter.blur(sigmaX: 10, sigmaY: 10),
          child: Container(
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(16),
              gradient: LinearGradient(
                begin: Alignment.topLeft,
                end: Alignment.bottomRight,
                colors: [
                  YourAITheme.pinkAccent.withOpacity(0.8),
                  YourAITheme.pinkAccent.withOpacity(0.6),
                ],
              ),
              border: Border.all(
                color: YourAITheme.pinkAccent.withOpacity(0.3),
                width: 1,
              ),
              boxShadow: [
                BoxShadow(
                  color: YourAITheme.pinkAccent.withOpacity(0.3),
                  blurRadius: 16,
                  offset: const Offset(0, 8),
                ),
              ],
            ),
            child: Material(
              color: Colors.transparent,
              child: InkWell(
                borderRadius: BorderRadius.circular(16),
                onTap: _logout,
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Icon(
                      Icons.logout,
                      color: Colors.white,
                      size: 24,
                    ),
                    const SizedBox(width: 12),
                    Text(
                      'Logout',
                      style: TextStyle(
                        fontFamily: YourAITheme.fontName,
                        fontWeight: FontWeight.w600,
                        fontSize: 18,
                        color: Colors.white,
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildInfoCard(
      String title, String value, IconData icon, Color iconColor) {
    return ClipRRect(
      borderRadius: BorderRadius.circular(16),
      child: BackdropFilter(
        filter: ImageFilter.blur(sigmaX: 10, sigmaY: 10),
        child: Container(
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(16),
            gradient: LinearGradient(
              begin: Alignment.topLeft,
              end: Alignment.bottomRight,
              colors: [
                Colors.white.withOpacity(0.2),
                Colors.white.withOpacity(0.1),
              ],
            ),
            border: Border.all(
              color: Colors.white.withOpacity(0.2),
              width: 1,
            ),
          ),
          child: Row(
            children: [
              ClipRRect(
                borderRadius: BorderRadius.circular(20),
                child: BackdropFilter(
                  filter: ImageFilter.blur(sigmaX: 5, sigmaY: 5),
                  child: Container(
                    height: 40,
                    width: 40,
                    decoration: BoxDecoration(
                      shape: BoxShape.circle,
                      gradient: LinearGradient(
                        begin: Alignment.topLeft,
                        end: Alignment.bottomRight,
                        colors: [
                          iconColor.withOpacity(0.3),
                          iconColor.withOpacity(0.1),
                        ],
                      ),
                      border: Border.all(
                        color: iconColor.withOpacity(0.4),
                        width: 0.5,
                      ),
                    ),
                    child: Icon(
                      icon,
                      color: Colors.white,
                      size: 20,
                    ),
                  ),
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      title,
                      style: TextStyle(
                        fontFamily: YourAITheme.fontName,
                        fontWeight: FontWeight.w600,
                        fontSize: 14,
                        color: Colors.white,
                      ),
                    ),
                    const SizedBox(height: 4),
                    Text(
                      value,
                      style: TextStyle(
                        fontFamily: YourAITheme.fontName,
                        fontWeight: FontWeight.w500,
                        fontSize: 16,
                        color: Colors.white,
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  String _formatDate(DateTime date) {
    return '${date.day}/${date.month}/${date.year} ${date.hour.toString().padLeft(2, '0')}:${date.minute.toString().padLeft(2, '0')}';
  }

  
}
