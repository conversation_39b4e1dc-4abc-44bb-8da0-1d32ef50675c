import 'package:flutter/material.dart';

class PulsatingDotIndicator extends StatefulWidget {
  const PulsatingDotIndicator({super.key});

  @override
  _PulsatingDotIndicatorState createState() => _PulsatingDotIndicatorState();
}

class _PulsatingDotIndicatorState extends State<PulsatingDotIndicator>
    with SingleTickerProviderStateMixin {
  late AnimationController _animationController;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 1000),
    )..repeat();
  }

  @override
  Widget build(BuildContext context) {
    return FadeTransition(
      opacity: _animationController,
      child: const Icon(
        Icons.more_horiz,
        color: Colors.grey,
        size: 32,
      ),
    );
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }
}
