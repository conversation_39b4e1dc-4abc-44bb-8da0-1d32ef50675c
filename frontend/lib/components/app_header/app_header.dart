import 'package:flutter/material.dart';
import 'package:best_flutter_ui_templates/yourai/yourai_theme.dart';

class AppHeader extends StatefulWidget {
  const AppHeader({
    Key? key,
    required this.animationController,
    required this.scrollController,
    this.title = 'My Diary',
    this.showDatePicker = true,
    this.selectedDate,
    this.onDateChanged,
    this.leading,
    this.action,
  }) : super(key: key);

  final AnimationController animationController;
  final ScrollController scrollController;
  final String title;
  final bool showDatePicker;
  final DateTime? selectedDate;
  final Function(DateTime?)? onDateChanged;
  final Widget? leading;
  final Widget? action;

  @override
  _AppHeaderState createState() => _AppHeaderState();
}

class _AppHeaderState extends State<AppHeader> {
  Animation<double>? topBarAnimation;
  double topBarOpacity = 0.0;

  @override
  void initState() {
    super.initState();
    topBarAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
        CurvedAnimation(
            parent: widget.animationController,
            curve: Interval(0, 0.5, curve: Curves.fastOutSlowIn)));

    widget.scrollController.addListener(() {
      if (widget.scrollController.offset >= 24) {
        if (topBarOpacity != 1.0) {
          setState(() {
            topBarOpacity = 1.0;
          });
        }
      } else if (widget.scrollController.offset <= 24 &&
          widget.scrollController.offset >= 0) {
        if (topBarOpacity != widget.scrollController.offset / 24) {
          setState(() {
            topBarOpacity = widget.scrollController.offset / 24;
          });
        }
      } else if (widget.scrollController.offset <= 0) {
        if (topBarOpacity != 0.0) {
          setState(() {
            topBarOpacity = 0.0;
          });
        }
      }
    });
  }

  String _formatDate(DateTime? date) {
    if (date == null) return '15 May';
    const months = ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun',
                   'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'];
    return '${date.day} ${months[date.month - 1]}';
  }

  void _goToPreviousDay() {
    if (widget.onDateChanged != null) {
      final currentDate = widget.selectedDate ?? DateTime.now();
      widget.onDateChanged!(currentDate.subtract(Duration(days: 1)));
    }
  }

  void _goToNextDay() {
    if (widget.onDateChanged != null) {
      final currentDate = widget.selectedDate ?? DateTime.now();
      widget.onDateChanged!(currentDate.add(Duration(days: 1)));
    }
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      children: <Widget>[
        AnimatedBuilder(
          animation: widget.animationController,
          builder: (BuildContext context, Widget? child) {
            return FadeTransition(
              opacity: topBarAnimation!,
              child: Transform(
                transform: Matrix4.translationValues(
                    0.0, 30 * (1.0 - topBarAnimation!.value), 0.0),
                child: Container(
                  decoration: BoxDecoration(
                    color: YourAITheme.surface.withOpacity(topBarOpacity),
                    borderRadius: const BorderRadius.only(
                      bottomLeft: Radius.circular(32.0),
                    ),
                    boxShadow: <BoxShadow>[
                      BoxShadow(
                          color: YourAITheme.grey
                              .withOpacity(0.4 * topBarOpacity),
                          offset: const Offset(1.1, 1.1),
                          blurRadius: 10.0),
                    ],
                  ),
                  child: Column(
                    children: <Widget>[
                      SizedBox(
                        height: MediaQuery.of(context).padding.top,
                      ),
                      Padding(
                        padding: EdgeInsets.only(
                            left: 16,
                            right: 16,
                            top: 16 - 8.0 * topBarOpacity,
                            bottom: 12 - 8.0 * topBarOpacity),
                        child: Row(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: <Widget>[
                            if (widget.leading != null) widget.leading!,
                            Expanded(
                              child: Padding(
                                padding: const EdgeInsets.all(8.0),
                                child: Text(
                                  widget.title,
                                  textAlign: TextAlign.left,
                                  style: TextStyle(
                                    fontFamily: YourAITheme.fontName,
                                    fontWeight: FontWeight.w700,
                                    fontSize: 22 + 6 - 6 * topBarOpacity,
                                    letterSpacing: 1.2,
                                    color: YourAITheme.darkerText,
                                  ),
                                ),
                              ),
                            ),
                            if (widget.action != null) widget.action!,
                            if (widget.showDatePicker) ...[
                              SizedBox(
                                height: 38,
                                width: 38,
                                child: InkWell(
                                  highlightColor: Colors.transparent,
                                  borderRadius: const BorderRadius.all(
                                      Radius.circular(32.0)),
                                  onTap: _goToPreviousDay,
                                  child: Center(
                                    child: Icon(
                                      Icons.keyboard_arrow_left,
                                      color: YourAITheme.darkerText,
                                    ),
                                  ),
                                ),
                              ),
                              Padding(
                                padding: const EdgeInsets.only(
                                  left: 8,
                                  right: 8,
                                ),
                                child: Row(
                                  children: <Widget>[
                                    Padding(
                                      padding: const EdgeInsets.only(right: 8),
                                      child: Icon(
                                        Icons.calendar_today,
                                        color: YourAITheme.darkerText,
                                        size: 18,
                                      ),
                                    ),
                                    Text(
                                      _formatDate(widget.selectedDate),
                                      textAlign: TextAlign.left,
                                      style: TextStyle(
                                        fontFamily: YourAITheme.fontName,
                                        fontWeight: FontWeight.normal,
                                        fontSize: 18,
                                        letterSpacing: -0.2,
                                        color: YourAITheme.darkerText,
                                      ),
                                    ),
                                  ],
                                ),
                              ),
                              SizedBox(
                                height: 38,
                                width: 38,
                                child: InkWell(
                                  highlightColor: Colors.transparent,
                                  borderRadius: const BorderRadius.all(
                                      Radius.circular(32.0)),
                                  onTap: _goToNextDay,
                                  child: Center(
                                    child: Icon(
                                      Icons.keyboard_arrow_right,
                                      color: YourAITheme.darkerText,
                                    ),
                                  ),
                                ),
                              ),
                            ],
                          ],
                        ),
                      )
                    ],
                  ),
                ),
              ),
            );
          },
        )
      ],
    );
  }
}