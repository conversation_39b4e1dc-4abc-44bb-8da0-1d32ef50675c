// API Client Service
// This service provides a centralized way to interact with the generated API client
// and handles common concerns like authentication, error handling, and configuration.

import 'package:dio/dio.dart';
import 'package:flutter/foundation.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:api_client/api_client.dart';

/// Service class that wraps the generated OpenAPI client with additional functionality
class ApiClientService {
  static ApiClientService? _instance;
  late final ApiClient _apiClient;
  late final Dio _dio;
  late final AgentApi _agentApi;
  late final GoogleApi _googleApi;
  late final LoginApi _loginApi;
  late final UsersApi _usersApi;
  
  // Private constructor
  ApiClientService._();
  
  /// Singleton instance getter
  static ApiClientService get instance {
    _instance ??= ApiClientService._();
    return _instance!;
  }
  
  /// Initialize the API client with configuration
  Future<void> initialize({
    String? baseUrl,
    Map<String, String>? defaultHeaders,
    Duration? connectTimeout,
    Duration? receiveTimeout,
  }) async {
    // Create API client with base path
    _apiClient = ApiClient(
      basePathOverride: baseUrl ?? 'http://localhost:8000',
    );

    // Get the Dio instance from the API client
    _dio = _apiClient.dio;

    // Initialize API endpoints using getter methods
    _agentApi = _apiClient.getAgentApi();
    _googleApi = _apiClient.getGoogleApi();
    _loginApi = _apiClient.getLoginApi();
    _usersApi = _apiClient.getUsersApi();

    // Load and set authentication token if available
    await _loadAuthToken();
  }
  
  /// Setup Dio interceptors for logging, authentication, and error handling
  void _setupInterceptors() {
    // Request interceptor for authentication
    _dio.interceptors.add(InterceptorsWrapper(
      onRequest: (options, handler) async {
        // Add authentication token if available
        final token = await _getAuthToken();
        if (token != null) {
          options.headers['Authorization'] = 'Bearer $token';
        }
        
        if (kDebugMode) {
          print('🚀 Request: ${options.method} ${options.uri}');
          if (options.data != null) {
            print('📤 Data: ${options.data}');
          }
        }
        
        handler.next(options);
      },
      onResponse: (response, handler) {
        if (kDebugMode) {
          print('✅ Response: ${response.statusCode} ${response.requestOptions.uri}');
        }
        handler.next(response);
      },
      onError: (error, handler) {
        if (kDebugMode) {
          print('❌ Error: ${error.response?.statusCode} ${error.requestOptions.uri}');
          print('Error message: ${error.message}');
        }
        
        // Handle specific error cases
        _handleApiError(error);
        
        handler.next(error);
      },
    ));
  }
  
  /// Handle API errors with custom logic
  void _handleApiError(DioException error) {
    switch (error.response?.statusCode) {
      case 401:
        // Unauthorized - clear token and redirect to login
        _clearAuthToken();
        break;
      case 403:
        // Forbidden - user doesn't have permission
        break;
      case 404:
        // Not found
        break;
      case 500:
        // Server error
        break;
      default:
        // Other errors
        break;
    }
  }
  
  /// Load authentication token from storage
  Future<void> _loadAuthToken() async {
    final token = await _getAuthToken();
    if (token != null) {
      _dio.options.headers['Authorization'] = 'Bearer $token';
    }
  }
  
  /// Get authentication token from secure storage
  Future<String?> _getAuthToken() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      return prefs.getString('auth_token');
    } catch (e) {
      if (kDebugMode) {
        print('Error getting auth token: $e');
      }
      return null;
    }
  }
  
  /// Set authentication token
  Future<void> setAuthToken(String token) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setString('auth_token', token);
      _dio.options.headers['Authorization'] = 'Bearer $token';
    } catch (e) {
      if (kDebugMode) {
        print('Error setting auth token: $e');
      }
    }
  }
  
  /// Clear authentication token
  Future<void> _clearAuthToken() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.remove('auth_token');
      _dio.options.headers.remove('Authorization');
    } catch (e) {
      if (kDebugMode) {
        print('Error clearing auth token: $e');
      }
    }
  }
  
  /// Get the OpenAPI client instance
  Openapi get client => _apiClient;
  
  /// Get the Dio instance for custom requests
  Dio get dio => _dio;
  
  /// Check if user is authenticated
  Future<bool> get isAuthenticated async {
    final token = await _getAuthToken();
    return token != null && token.isNotEmpty;
  }
  
  /// Logout user by clearing authentication
  Future<void> logout() async {
    await _clearAuthToken();
  }
  
  /// Update base URL (useful for switching environments)
  void updateBaseUrl(String baseUrl) {
    _dio.options.baseUrl = baseUrl;
  }
  
  /// Add custom header
  void addHeader(String key, String value) {
    _dio.options.headers[key] = value;
  }
  
  /// Remove custom header
  void removeHeader(String key) {
    _dio.options.headers.remove(key);
  }
}
