// Authentication Service
// This service handles all authentication-related operations using the generated API client

import 'package:dio/dio.dart';
import 'package:flutter/foundation.dart';
import 'package:google_sign_in/google_sign_in.dart';
import 'package:api_client/api_client.dart';
import 'package:shared_preferences/shared_preferences.dart';

import '../config/api_config.dart';
import 'api_client_service.dart';

/// Exception thrown when authentication fails
class AuthException implements Exception {
  final String message;
  final int? statusCode;
  
  const AuthException(this.message, [this.statusCode]);
  
  @override
  String toString() => 'AuthException: $message';
}

/// Authentication service that handles login, logout, and token management
class AuthService {
  static AuthService? _instance;
  late final GoogleSignIn _googleSignIn;
  late final ApiClientService _apiClient;
  
  // Private constructor
  AuthService._() {
    _googleSignIn = GoogleSignIn(
      scopes: ['email', 'profile'],
    );
    _apiClient = ApiClientService.instance;
  }
  
  /// Singleton instance getter
  static AuthService get instance {
    _instance ??= AuthService._();
    return _instance!;
  }
  
  /// Initialize the authentication service
  Future<void> initialize() async {
    // Initialize API client if not already done
    if (!(await _apiClient.isAuthenticated)) {
      await _apiClient.initialize(
        baseUrl: ApiConfig.baseUrl,
        defaultHeaders: ApiConfig.defaultHeaders,
        connectTimeout: ApiConfig.connectTimeout,
        receiveTimeout: ApiConfig.receiveTimeout,
      );
    }
  }
  
  /// Login with Google OAuth
  Future<UserPublic> loginWithGoogle() async {
    try {
      // Sign in with Google
      final GoogleSignInAccount? googleUser = await _googleSignIn.signIn();
      if (googleUser == null) {
        throw const AuthException('Google sign-in was cancelled');
      }
      
      // Get authentication details
      final GoogleSignInAuthentication googleAuth = await googleUser.authentication;
      final accessToken = googleAuth.accessToken;
      if (accessToken == null) {
        throw const AuthException('Failed to get Google access token');
      }

      // Call backend API for authentication
      final response = await _apiClient.client.getGoogleApi().googleGoogleCallback(
        headers: {
          'Authorization': 'Bearer $accessToken',
        },
      );
      
      // Handle the response based on its type
      if (response.data is Map<String, dynamic>) {
        final data = response.data as Map<String, dynamic>;
        
        // Extract token and user information
        final accessToken = data['access_token'] as String?;
        final userData = data['user'] as Map<String, dynamic>?;
        
        if (accessToken == null) {
          throw const AuthException('No access token received from server');
        }
        
        // Store the token
        await _apiClient.setAuthToken(accessToken);
        
        // Store user information
        if (userData != null) {
          await _storeUserData(userData);
        }
        
        // Return user object
        if (userData != null) {
          return _apiClient.client.serializers.deserializeWith(
            UserPublic.serializer,
            userData,
          );
        } else {
          // Fetch user data if not included in response
          return await getCurrentUser();
        }
      } else {
        throw const AuthException('Invalid response format from server');
      }
    } on DioException catch (e) {
      throw _handleDioException(e);
    } catch (e) {
      if (e is AuthException) rethrow;
      throw AuthException('Google login failed: ${e.toString()}');
    }
  }
  
  /// Login with email and password
  Future<UserPublic> loginWithEmailPassword(String email, String password) async {
    try {
      final response = await _apiClient.client.getLoginApi().loginLoginAccessToken(
        username: email,
        password: password,
        grantType: ApiConfig.grantType,
      );
      
      if (response.data == null) {
        throw const AuthException('No response data received');
      }
      
      final token = response.data!;
      
      // Store the access token
      await _apiClient.setAuthToken(token.accessToken);
      
      // Get current user information
      return await getCurrentUser();
    } on DioException catch (e) {
      throw _handleDioException(e);
    } catch (e) {
      if (e is AuthException) rethrow;
      throw AuthException('Email login failed: ${e.toString()}');
    }
  }
  
  /// Register a new user
  Future<UserPublic> register(String email, String password, String? fullName) async {
    try {
      final response = await _apiClient.client.getUsersApi().usersRegisterUser(
        userRegister: UserRegister((b) => b
          ..email = email
          ..password = password
          ..fullName = fullName),
      );
      
      if (response.data == null) {
        throw const AuthException('No response data received');
      }
      
      return response.data!;
    } on DioException catch (e) {
      throw _handleDioException(e);
    } catch (e) {
      if (e is AuthException) rethrow;
      throw AuthException('Registration failed: ${e.toString()}');
    }
  }
  
  /// Get current user information
  Future<UserPublic> getCurrentUser() async {
    try {
      final response = await _apiClient.client.getUsersApi().usersReadUserMe();
      
      if (response.data == null) {
        throw const AuthException('No user data received');
      }
      
      // Store user data locally
      await _storeUserData(response.data!.toJson());
      
      return response.data!;
    } on DioException catch (e) {
      throw _handleDioException(e);
    } catch (e) {
      if (e is AuthException) rethrow;
      throw AuthException('Failed to get current user: ${e.toString()}');
    }
  }
  
  /// Logout user
  Future<void> logout() async {
    try {
      // Sign out from Google
      await _googleSignIn.signOut();
      
      // Clear API client authentication
      await _apiClient.logout();
      
      // Clear stored user data
      await _clearUserData();
    } catch (e) {
      if (kDebugMode) {
        print('Logout error: $e');
      }
      // Even if logout fails, clear local data
      await _apiClient.logout();
      await _clearUserData();
    }
  }
  
  /// Check if user is authenticated
  Future<bool> isAuthenticated() async {
    return await _apiClient.isAuthenticated;
  }
  
  /// Test current token validity
  Future<bool> testToken() async {
    try {
      await _apiClient.client.getLoginApi().loginTestToken();
      return true;
    } catch (e) {
      return false;
    }
  }
  
  /// Store user data locally
  Future<void> _storeUserData(Map<String, dynamic> userData) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setString('user_data', userData.toString());
    } catch (e) {
      if (kDebugMode) {
        print('Error storing user data: $e');
      }
    }
  }
  
  /// Clear stored user data
  Future<void> _clearUserData() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.remove('user_data');
    } catch (e) {
      if (kDebugMode) {
        print('Error clearing user data: $e');
      }
    }
  }
  
  /// Handle Dio exceptions and convert to AuthException
  AuthException _handleDioException(DioException e) {
    final statusCode = e.response?.statusCode;
    final message = e.response?.data?['detail'] ?? e.message ?? 'Unknown error';
    
    switch (statusCode) {
      case 400:
        return AuthException('Invalid request: $message', statusCode);
      case 401:
        return AuthException('Invalid credentials', statusCode);
      case 403:
        return AuthException('Access forbidden', statusCode);
      case 404:
        return AuthException('Service not found', statusCode);
      case 422:
        return AuthException('Validation error: $message', statusCode);
      case 500:
        return AuthException('Server error. Please try again later.', statusCode);
      default:
        return AuthException('Authentication failed: $message', statusCode);
    }
  }
}
