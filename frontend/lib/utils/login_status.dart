import 'package:flutter/material.dart';
import 'package:api_client/api_client.dart';

class LoginStatus with ChangeNotifier {
  bool _isLoggedIn = false;
  String? _accessToken;
  UserPublic? _user;

  bool get isLoggedIn => _isLoggedIn;
  String? get accessToken => _accessToken;
  UserPublic? get user => _user;

  void login({String? accessToken, UserPublic? user}) {
    _isLoggedIn = true;
    _accessToken = accessToken;
    _user = user;
    notifyListeners();
  }

  void logout() {
    _isLoggedIn = false;
    _accessToken = null;
    _user = null;
    notifyListeners();
  }

  void setUser(UserPublic user) {
    _user = user;
    notifyListeners();
  }
}
