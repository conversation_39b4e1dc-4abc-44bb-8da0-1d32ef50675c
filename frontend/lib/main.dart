import 'dart:io';
import 'package:best_flutter_ui_templates/yourai/yourai_theme.dart';
import 'package:best_flutter_ui_templates/yourai/yourai_home_screen.dart';
import 'package:best_flutter_ui_templates/auth/auth_screen.dart';
import 'package:best_flutter_ui_templates/utils/login_status.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter/foundation.dart' show kIsWeb;
import 'package:provider/provider.dart';

void main() async {
  WidgetsFlutterBinding.ensureInitialized();
  await SystemChrome.setPreferredOrientations(<DeviceOrientation>[
    DeviceOrientation.portraitUp,
    DeviceOrientation.portraitDown
  ]).then((_) => runApp(
        ChangeNotifierProvider(
          create: (context) => LoginStatus(),
          child: const MyApp(),
        ),
      ));
}

class MyApp extends StatelessWidget {
  const MyApp({super.key});

  @override
  Widget build(BuildContext context) {
    SystemChrome.setSystemUIOverlayStyle(SystemUiOverlayStyle(
      statusBarColor: Colors.transparent,
      statusBarIconBrightness: Brightness.light,
      statusBarBrightness:
          !kIsWeb && Platform.isAndroid ? Brightness.dark : Brightness.light,
      systemNavigationBarColor: YourAITheme.background,
      systemNavigationBarDividerColor: Colors.transparent,
      systemNavigationBarIconBrightness: Brightness.light,
    ));
    return MaterialApp(
      title: 'YourAI',
      debugShowCheckedModeBanner: false,
      themeMode: ThemeMode.dark,
      darkTheme: ThemeData.dark().copyWith(
        primaryColor: YourAITheme.nearlyDarkBlue,
        scaffoldBackgroundColor: YourAITheme.background,
        appBarTheme: AppBarTheme(
          backgroundColor: YourAITheme.surface,
          foregroundColor: YourAITheme.darkerText,
        ),
        textTheme: YourAITheme.textTheme,
        platform: TargetPlatform.iOS,
      ),
      theme: ThemeData.dark().copyWith(
        primaryColor: YourAITheme.nearlyDarkBlue,
        scaffoldBackgroundColor: YourAITheme.background,
        textTheme: YourAITheme.textTheme,
        platform: TargetPlatform.iOS,
      ),
      home: Consumer<LoginStatus>(
        builder: (context, loginStatus, child) {
          return loginStatus.isLoggedIn
              ? YourAIHomeScreen()
              : const AuthScreen();
        },
      ),
    );
  }
}