import 'package:flutter/foundation.dart';
import 'package:best_flutter_ui_templates/chat/streaming_chat_service.dart';
import 'package:best_flutter_ui_templates/services/api_client_service.dart';

/// Test class for streaming chat functionality
class StreamingTest {
  static Future<void> testStreamingChat() async {
    if (kDebugMode) {
      print('🧪 Testing streaming chat functionality...');
    }

    try {
      // Initialize services
      await ApiClientService.instance.initialize();
      
      final chatService = StreamingChatService.instance;
      
      // Test simple message
      if (kDebugMode) {
        print('📤 Sending test message...');
      }
      
      final stream = await chatService.sendTextMessage(
        'Hello! Can you tell me a short joke?',
      );

      String? sessionId;
      String fullResponse = '';
      
      await for (final event in stream) {
        switch (event.type) {
          case ChatEventType.sessionId:
            sessionId = event.data as String;
            if (kDebugMode) {
              print('🆔 Session ID: $sessionId');
            }
            break;
            
          case ChatEventType.textChunk:
            final chunk = event.data as String;
            fullResponse += chunk;
            if (kDebugMode) {
              print('📝 Text chunk: $chunk');
            }
            break;
            
          case ChatEventType.toolStart:
            final toolData = event.data as Map<String, dynamic>;
            if (kDebugMode) {
              print('🔧 Tool started: ${toolData['name']} with args: ${toolData['args']}');
            }
            break;
            
          case ChatEventType.toolEnd:
            final toolData = event.data as Map<String, dynamic>;
            if (kDebugMode) {
              print('✅ Tool completed: ${toolData['name']} with output: ${toolData['output']}');
            }
            break;
            
          case ChatEventType.error:
            if (kDebugMode) {
              print('❌ Error: ${event.data}');
            }
            break;
            
          case ChatEventType.done:
            if (kDebugMode) {
              print('✅ Stream completed');
            }
            break;
        }
      }
      
      if (kDebugMode) {
        print('📋 Full response: $fullResponse');
        print('🎉 Streaming test completed successfully!');
      }
      
    } catch (e) {
      if (kDebugMode) {
        print('💥 Streaming test failed: $e');
      }
      rethrow;
    }
  }

  /// Test streaming with images
  static Future<void> testStreamingWithImages() async {
    if (kDebugMode) {
      print('🧪 Testing streaming chat with images...');
    }

    try {
      await ApiClientService.instance.initialize();
      
      final chatService = StreamingChatService.instance;
      
      // Note: This would require actual image files to test
      // For now, just test the API structure
      if (kDebugMode) {
        print('📸 Image streaming test would require actual image files');
        print('✅ Image streaming API structure is ready');
      }
      
    } catch (e) {
      if (kDebugMode) {
        print('💥 Image streaming test failed: $e');
      }
      rethrow;
    }
  }

  /// Test error handling
  static Future<void> testErrorHandling() async {
    if (kDebugMode) {
      print('🧪 Testing error handling...');
    }

    try {
      // Don't initialize services to test error handling
      final chatService = StreamingChatService.instance;
      
      final stream = await chatService.sendTextMessage('Test message');
      
      await for (final event in stream) {
        if (event.type == ChatEventType.error) {
          if (kDebugMode) {
            print('✅ Error handling works: ${event.data}');
          }
          break;
        }
      }
      
    } catch (e) {
      if (kDebugMode) {
        print('✅ Exception handling works: $e');
      }
    }
  }

  /// Run all tests
  static Future<void> runAllTests() async {
    if (kDebugMode) {
      print('🚀 Running all streaming tests...');
    }

    await testErrorHandling();
    await testStreamingChat();
    await testStreamingWithImages();
    
    if (kDebugMode) {
      print('🎊 All streaming tests completed!');
    }
  }
}
