import 'dart:io';
import 'package:best_flutter_ui_templates/chat/chat_service.dart';
import 'package:http/http.dart' as http;
import 'package:best_flutter_ui_templates/chat/chat_input_field.dart';
import 'package:best_flutter_ui_templates/chat/chat_message.dart';
import 'package:best_flutter_ui_templates/chat/conversation_drawer.dart';
import 'package:best_flutter_ui_templates/chat/typing_indicator.dart';
import 'package:best_flutter_ui_templates/components/app_header/app_header.dart';
import 'package:best_flutter_ui_templates/components/glassmorphic_button.dart';
import 'package:best_flutter_ui_templates/constants.dart';
import 'package:best_flutter_ui_templates/yourai/yourai_theme.dart';
import 'package:flutter/material.dart';
import 'package:uuid/uuid.dart';
import 'package:openapi/openapi.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:dio/dio.dart' as dio;

class ChatScreen extends StatefulWidget {
  const ChatScreen({super.key});

  @override
  State<ChatScreen> createState() => _ChatScreenState();
}

class _ChatScreenState extends State<ChatScreen> with TickerProviderStateMixin {
  final List<ChatMessage> _messages = <ChatMessage>[];
  bool _isBotTyping = false;
  late final AnimationController animationController;
  final ScrollController scrollController = ScrollController();
  final GlobalKey<ScaffoldState> _scaffoldKey = GlobalKey<ScaffoldState>();
  String? sessionId;
  final ChatService _chatService = ChatService();
  late final Openapi openapi;

  late final Future<void> _initializationFuture;

  @override
  void initState() {
    super.initState();
    animationController = AnimationController(
        duration: const Duration(milliseconds: 600), vsync: this);
    animationController.forward();
    _initializationFuture = _initializeApi();
  }

  Future<void> _initializeApi() async {
    final token = await _getToken();
    if (token == null) {
      throw Exception('Authentication token not found. Please log in again.');
    }
    final dioClient = dio.Dio();
    dioClient.options.headers['Authorization'] = 'Bearer $token';
    dioClient.options.baseUrl = Constants.apiBaseUrl;
    openapi = Openapi(dio: dioClient);
  }

  Future<String?> _getToken() async {
    final prefs = await SharedPreferences.getInstance();
    return prefs.getString(Constants.authTokenKey);
  }

  @override
  void dispose() {
    animationController.dispose();
    super.dispose();
  }

  void _handleSendMessage(String text) {
    _handleSendMessageWithImages(text, []);
  }

  void _handleSendMessageWithImages(String text, List<File> images) async {
    final userMessage = ChatMessage(
      text: text,
      isUserMessage: true,
      images: images.isNotEmpty ? images : null,
    );
    setState(() {
      _messages.insert(0, userMessage);
      _isBotTyping = true;
    });

    try {
      final imageFiles = images.map((image) {
        return http.MultipartFile.fromBytes(
          'images',
          image.readAsBytesSync(),
          filename: image.path.split('/').last,
        );
      }).toList();

      final stream = await _chatService.sendMessage(
        message: text,
        sessionId: sessionId,
        imageFiles: imageFiles,
      );

      stream.listen((event) {
        _handleStreamEvent(event);
      }, onDone: () {
        setState(() {
          _isBotTyping = false;
        });
      }, onError: (error) {
        _showErrorMessage(error.toString());
        setState(() {
          _isBotTyping = false;
        });
      });
    } catch (e) {
      _showErrorMessage(e.toString());
      setState(() {
        _isBotTyping = false;
      });
    }
  }

  void _handleStreamEvent(Map<String, dynamic> event) {
    final type = event['type'];
    final data = event['data'];

    setState(() {
      switch (type) {
        case 'session_id':
          sessionId = data;
          break;
        case 'text_chunk':
          if (_messages.first.isUserMessage) {
            _messages.insert(0, ChatMessage(text: data, isUserMessage: false));
          } else {
            final lastMessage = _messages.first;
            final updatedMessage = ChatMessage(
              text: (lastMessage.text ?? '') + data,
              isUserMessage: false,
            );
            _messages[0] = updatedMessage;
          }
          break;
        case 'tool_start':
          final toolCall = {
            'name': data['name'],
            'args': data['args'],
            'output': null,
          };
          _messages.insert(0, ChatMessage(toolCall: toolCall, isUserMessage: false));
          break;
        case 'tool_end':
          final toolName = data['name'];
          final toolOutput = data['output'];
          final toolMessageIndex = _messages.indexWhere((m) =>
              m.toolCall != null &&
              m.toolCall!['name'] == toolName &&
              m.toolCall!['output'] == null);
          if (toolMessageIndex != -1) {
            final oldMessage = _messages[toolMessageIndex];
            final updatedToolCall = Map<String, dynamic>.from(oldMessage.toolCall!);
            updatedToolCall['output'] = toolOutput;
            _messages[toolMessageIndex] = ChatMessage(
              toolCall: updatedToolCall,
              isUserMessage: false,
            );
          }
          break;
        case 'error':
          _showErrorMessage(data);
          break;
      }
    });
  }

  void _showErrorMessage(String message) {
    final errorMessage = ChatMessage(
      text: message,
      isUserMessage: false,
    );
    setState(() {
      _messages.insert(0, errorMessage);
    });
  }

  Future<void> _loadConversationHistory(String newSessionId) async {
    try {
      final response = await openapi.getAgentApi().agentGetConversationHistory(sessionId: newSessionId);
      if (response.statusCode == 200 && response.data != null) {
        final history = response.data!.history;
        final loadedMessages = history.map((item) {
          if (item != null) {
            Map<String, dynamic>? messageData;
            // Handle different possible formats
            if (item.value is Map<String, dynamic>) {
              messageData = item.value as Map<String, dynamic>;
            } else if (item.value is Map) {
              try {
                messageData = Map<String, dynamic>.from(item.value as Map);
              } catch (e) {
                print('Error converting Map to Map<String, dynamic>: $e');
                return null;
              }
            }
            if (messageData != null && messageData.containsKey('role') && messageData.containsKey('content')) {
              final role = messageData['role'];
              final content = messageData['content'];
              String text = '';
              if (content is String) {
                text = content;
              } else if (content is List) {
                // agno format: list of dicts, find first type=="text"
                final textItem = content.firstWhere(
                  (el) => el is Map && el['type'] == 'text' && el['text'] != null,
                  orElse: () => null,
                );
                if (textItem != null && textItem is Map && textItem['text'] != null) {
                  text = textItem['text'].toString();
                } else {
                  text = content.toString();
                }
              } else {
                text = content.toString();
              }
              if (text.isNotEmpty && role != null) {
                return ChatMessage(
                  text: text,
                  isUserMessage: role.toString() == 'user',
                );
              }
            }
          }
          return null;
        }).whereType<ChatMessage>().toList();
        setState(() {
          sessionId = newSessionId;
          _messages.clear();
          _messages.addAll(loadedMessages.reversed);
        });
      }
    } catch (e) {
      print('Error loading conversation history: $e');
      _showErrorMessage('Failed to load conversation history');
    }
  }

  @override
  Widget build(BuildContext context) {
    return FutureBuilder<void>(
      future: _initializationFuture,
      builder: (context, snapshot) {
        if (snapshot.connectionState == ConnectionState.waiting) {
          return const Scaffold(
            body: Center(
              child: CircularProgressIndicator(),
            ),
          );
        } else if (snapshot.hasError) {
          return Scaffold(
            body: Center(
              child: Text('Error initializing: ${snapshot.error}'),
            ),
          );
        }
        return Scaffold(
          key: _scaffoldKey,
          backgroundColor: YourAITheme.background,
          drawer: ConversationDrawer(
            onConversationSelected: _loadConversationHistory,
          ),
          body: Stack(
            children: [
              Positioned.fill(
                child: Column(
                  children: <Widget>[
                    SizedBox(
                      height: AppBar().preferredSize.height +
                          MediaQuery.of(context).padding.top +
                          24,
                    ),
                    Flexible(
                      child: ListView.builder(
                        padding: const EdgeInsets.all(8.0),
                        reverse: true,
                        itemBuilder: (_, int index) {
                          if (index == 0 && _isBotTyping) {
                            return const TypingIndicator();
                          }
                          final messageIndex = _isBotTyping ? index - 1 : index;
                          if (messageIndex < 0 ||
                              messageIndex >= _messages.length) {
                            return const SizedBox.shrink();
                          }
                          return _messages[messageIndex];
                        },
                        itemCount: _messages.length + (_isBotTyping ? 1 : 0),
                      ),
                    ),
                    Padding(
                      padding: const EdgeInsets.only(bottom: 80.0),
                      child: ChatInputField(
                        onSendMessage: _handleSendMessage,
                        onSendMessageWithImages: _handleSendMessageWithImages,
                      ),
                    ),
                  ],
                ),
              ),
              AppHeader(
                animationController: animationController,
                scrollController: scrollController,
                title: 'Chatbot',
                showDatePicker: false,
                leading: IconButton(
                  icon: const Icon(Icons.menu),
                  onPressed: () {
                    _scaffoldKey.currentState?.openDrawer();
                  },
                ),
                action: GlassmorphicButton(
                  icon: Icons.add_comment_outlined,
                  onPressed: () {
                    setState(() {
                      sessionId = const Uuid().v4();
                      _messages.clear();
                    });
                  },
                ),
              ),
            ],
          ),
        );
      },
    );
  }
}

