import 'dart:async';
import 'dart:io';
import 'package:best_flutter_ui_templates/chat/streaming_chat_service.dart';
import 'package:best_flutter_ui_templates/chat/chat_input_field.dart';
import 'package:best_flutter_ui_templates/chat/chat_message.dart';
import 'package:best_flutter_ui_templates/chat/conversation_drawer.dart';
import 'package:best_flutter_ui_templates/chat/typing_indicator.dart';
import 'package:best_flutter_ui_templates/components/app_header/app_header.dart';
import 'package:best_flutter_ui_templates/components/glassmorphic_button.dart';
import 'package:best_flutter_ui_templates/constants.dart';
import 'package:best_flutter_ui_templates/yourai/yourai_theme.dart';
import 'package:best_flutter_ui_templates/services/api_client_service.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:uuid/uuid.dart';
import 'package:api_client/api_client.dart';

class ChatScreen extends StatefulWidget {
  const ChatScreen({super.key});

  @override
  State<ChatScreen> createState() => _ChatScreenState();
}

class _ChatScreenState extends State<ChatScreen> with TickerProviderStateMixin {
  final List<ChatMessage> _messages = <ChatMessage>[];
  bool _isBotTyping = false;
  late final AnimationController animationController;
  final ScrollController scrollController = ScrollController();
  final GlobalKey<ScaffoldState> _scaffoldKey = GlobalKey<ScaffoldState>();
  String? sessionId;
  final StreamingChatService _chatService = StreamingChatService.instance;
  StreamSubscription<ChatEvent>? _streamSubscription;

  late final Future<void> _initializationFuture;

  @override
  void initState() {
    super.initState();
    animationController = AnimationController(
        duration: const Duration(milliseconds: 600), vsync: this);
    animationController.forward();
    _initializationFuture = _initializeServices();
  }

  Future<void> _initializeServices() async {
    try {
      // Initialize API client service
      await ApiClientService.instance.initialize();
    } catch (e) {
      throw Exception('Failed to initialize services: $e');
    }
  }

  @override
  void dispose() {
    _streamSubscription?.cancel();
    animationController.dispose();
    super.dispose();
  }

  void _handleSendMessage(String text) {
    _handleSendMessageWithImages(text, []);
  }

  void _handleSendMessageWithImages(String text, List<File> images) async {
    // Cancel any existing stream subscription
    await _streamSubscription?.cancel();

    final userMessage = ChatMessage(
      text: text,
      isUserMessage: true,
      images: images.isNotEmpty ? images : null,
    );

    setState(() {
      _messages.insert(0, userMessage);
      _isBotTyping = true;
    });

    try {
      final stream = await _chatService.sendMessage(
        message: text,
        sessionId: sessionId,
        images: images.isNotEmpty ? images : null,
      );

      _streamSubscription = stream.listen(
        (event) {
          _handleChatEvent(event);
        },
        onDone: () {
          setState(() {
            _isBotTyping = false;
          });
          _streamSubscription = null;
        },
        onError: (error) {
          _showErrorMessage('Stream error: $error');
          setState(() {
            _isBotTyping = false;
          });
          _streamSubscription = null;
        },
        cancelOnError: false,
      );
    } catch (e) {
      _showErrorMessage('Failed to send message: $e');
      setState(() {
        _isBotTyping = false;
      });
    }
  }

  void _handleChatEvent(ChatEvent event) {
    setState(() {
      switch (event.type) {
        case ChatEventType.sessionId:
          sessionId = event.data as String;
          if (kDebugMode) {
            print('Session ID updated: $sessionId');
          }
          break;

        case ChatEventType.textChunk:
          final textChunk = event.data as String;
          if (_messages.isEmpty || _messages.first.isUserMessage) {
            // Create new bot message
            _messages.insert(0, ChatMessage(text: textChunk, isUserMessage: false));
          } else {
            // Append to existing bot message
            final lastMessage = _messages.first;
            final updatedMessage = ChatMessage(
              text: (lastMessage.text ?? '') + textChunk,
              isUserMessage: false,
            );
            _messages[0] = updatedMessage;
          }
          break;

        case ChatEventType.toolStart:
          final toolData = event.data as Map<String, dynamic>;
          final toolCall = {
            'name': toolData['name'],
            'args': toolData['args'],
            'output': null,
          };
          _messages.insert(0, ChatMessage(toolCall: toolCall, isUserMessage: false));
          break;

        case ChatEventType.toolEnd:
          final toolData = event.data as Map<String, dynamic>;
          final toolName = toolData['name'];
          final toolOutput = toolData['output'];
          final toolMessageIndex = _messages.indexWhere((m) =>
              m.toolCall != null &&
              m.toolCall!['name'] == toolName &&
              m.toolCall!['output'] == null);
          if (toolMessageIndex != -1) {
            final oldMessage = _messages[toolMessageIndex];
            final updatedToolCall = Map<String, dynamic>.from(oldMessage.toolCall!);
            updatedToolCall['output'] = toolOutput;
            _messages[toolMessageIndex] = ChatMessage(
              toolCall: updatedToolCall,
              isUserMessage: false,
            );
          }
          break;

        case ChatEventType.error:
          _showErrorMessage(event.data.toString());
          _isBotTyping = false;
          break;

        case ChatEventType.done:
          _isBotTyping = false;
          if (kDebugMode) {
            print('Stream completed');
          }
          break;
      }
    });
  }

  void _showErrorMessage(String message) {
    final errorMessage = ChatMessage(
      text: message,
      isUserMessage: false,
    );
    setState(() {
      _messages.insert(0, errorMessage);
    });
  }

  Future<void> _loadConversationHistory(String newSessionId) async {
    try {
      final apiService = ApiClientService.instance;
      final agentApi = apiService.agentApi;

      final response = await agentApi.agentGetConversationHistory(sessionId: newSessionId);

      if (response.statusCode == 200 && response.data != null) {
        final conversation = response.data!;
        final history = conversation.history;

        final loadedMessages = <ChatMessage>[];

        for (final item in history) {
          if (item != null) {
            Map<String, dynamic>? messageData;

            // Handle different possible formats
            if (item.value is Map<String, dynamic>) {
              messageData = item.value as Map<String, dynamic>;
            } else if (item.value is Map) {
              try {
                messageData = Map<String, dynamic>.from(item.value as Map);
              } catch (e) {
                if (kDebugMode) {
                  print('Error converting Map to Map<String, dynamic>: $e');
                }
                continue;
              }
            }

            if (messageData != null &&
                messageData.containsKey('role') &&
                messageData.containsKey('content')) {
              final role = messageData['role'];
              final content = messageData['content'];
              String text = '';

              if (content is String) {
                text = content;
              } else if (content is List) {
                // Handle agno format: list of dicts, find first type=="text"
                final textItem = content.firstWhere(
                  (el) => el is Map && el['type'] == 'text' && el['text'] != null,
                  orElse: () => null,
                );
                if (textItem != null && textItem is Map && textItem['text'] != null) {
                  text = textItem['text'].toString();
                } else {
                  text = content.toString();
                }
              } else {
                text = content.toString();
              }

              if (text.isNotEmpty && role != null) {
                loadedMessages.add(ChatMessage(
                  text: text,
                  isUserMessage: role.toString() == 'user',
                ));
              }
            }
          }
        }

        setState(() {
          sessionId = newSessionId;
          _messages.clear();
          _messages.addAll(loadedMessages.reversed);
        });
      }
    } catch (e) {
      if (kDebugMode) {
        print('Error loading conversation history: $e');
      }
      _showErrorMessage('Failed to load conversation history: $e');
    }
  }

  @override
  Widget build(BuildContext context) {
    return FutureBuilder<void>(
      future: _initializationFuture,
      builder: (context, snapshot) {
        if (snapshot.connectionState == ConnectionState.waiting) {
          return const Scaffold(
            body: Center(
              child: CircularProgressIndicator(),
            ),
          );
        } else if (snapshot.hasError) {
          return Scaffold(
            body: Center(
              child: Text('Error initializing: ${snapshot.error}'),
            ),
          );
        }
        return Scaffold(
          key: _scaffoldKey,
          backgroundColor: YourAITheme.background,
          drawer: ConversationDrawer(
            onConversationSelected: _loadConversationHistory,
          ),
          body: Stack(
            children: [
              Positioned.fill(
                child: Column(
                  children: <Widget>[
                    SizedBox(
                      height: AppBar().preferredSize.height +
                          MediaQuery.of(context).padding.top +
                          24,
                    ),
                    Flexible(
                      child: ListView.builder(
                        padding: const EdgeInsets.all(8.0),
                        reverse: true,
                        itemBuilder: (_, int index) {
                          if (index == 0 && _isBotTyping) {
                            return const TypingIndicator();
                          }
                          final messageIndex = _isBotTyping ? index - 1 : index;
                          if (messageIndex < 0 ||
                              messageIndex >= _messages.length) {
                            return const SizedBox.shrink();
                          }
                          return _messages[messageIndex];
                        },
                        itemCount: _messages.length + (_isBotTyping ? 1 : 0),
                      ),
                    ),
                    Padding(
                      padding: const EdgeInsets.only(bottom: 80.0),
                      child: ChatInputField(
                        onSendMessage: _handleSendMessage,
                        onSendMessageWithImages: _handleSendMessageWithImages,
                      ),
                    ),
                  ],
                ),
              ),
              AppHeader(
                animationController: animationController,
                scrollController: scrollController,
                title: 'Chatbot',
                showDatePicker: false,
                leading: IconButton(
                  icon: const Icon(Icons.menu),
                  onPressed: () {
                    _scaffoldKey.currentState?.openDrawer();
                  },
                ),
                action: GlassmorphicButton(
                  icon: Icons.add_comment_outlined,
                  onPressed: () {
                    setState(() {
                      sessionId = const Uuid().v4();
                      _messages.clear();
                    });
                  },
                ),
              ),
            ],
          ),
        );
      },
    );
  }
}

