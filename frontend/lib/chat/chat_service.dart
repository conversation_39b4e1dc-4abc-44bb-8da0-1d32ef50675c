import 'dart:async';
import 'dart:convert';
import 'dart:io';
import 'dart:typed_data';
import 'package:dio/dio.dart';
import 'package:flutter/foundation.dart';
import 'package:best_flutter_ui_templates/services/api_client_service.dart';

/// Enhanced chat service with improved streaming support
class ChatService {
  static ChatService? _instance;
  static ChatService get instance => _instance ??= ChatService._();

  ChatService._();

  /// Send a message with streaming response support
  Future<Stream<Map<String, dynamic>>> sendMessage({
    required String message,
    String? sessionId,
    List<File>? images,
  }) async {
    try {
      final apiService = ApiClientService.instance;

      // Prepare multipart files for images
      List<MultipartFile>? multipartFiles;
      if (images != null && images.isNotEmpty) {
        multipartFiles = [];
        for (final image in images) {
          final bytes = await image.readAsBytes();
          final multipartFile = MultipartFile.fromBytes(
            bytes,
            filename: image.path.split('/').last,
          );
          multipartFiles.add(multipartFile);
        }
      }

      // Create form data
      final formData = FormData.fromMap({
        'message': message,
        if (sessionId != null) 'session_id': sessionId,
        if (multipartFiles != null) 'images': multipartFiles,
      });

      // Make streaming request using Dio
      final response = await apiService.dio.post<ResponseBody>(
        '/api/v1/agent/chat',
        data: formData,
        options: Options(
          responseType: ResponseType.stream,
          headers: {
            'Accept': 'text/event-stream',
            'Cache-Control': 'no-cache',
          },
        ),
      );

      if (response.statusCode == 200 && response.data != null) {
        return _parseSSEStream(response.data!.stream);
      } else {
        throw Exception('Failed to send message: ${response.statusCode}');
      }
    } catch (e) {
      if (kDebugMode) {
        print('Error in sendMessage: $e');
      }
      rethrow;
    }
  }

  /// Parse Server-Sent Events stream
  Stream<Map<String, dynamic>> _parseSSEStream(Stream<Uint8List> stream) {
    return stream
        .transform(utf8.decoder)
        .transform(const LineSplitter())
        .where((line) => line.trim().isNotEmpty)
        .where((line) => line.startsWith('data: '))
        .map((line) {
          try {
            final jsonString = line.substring(6); // Remove 'data: ' prefix
            return jsonDecode(jsonString) as Map<String, dynamic>;
          } catch (e) {
            if (kDebugMode) {
              print('Error parsing SSE line: $line, error: $e');
            }
            return <String, dynamic>{
              'type': 'error',
              'data': 'Failed to parse server response'
            };
          }
        })
        .handleError((error) {
          if (kDebugMode) {
            print('Stream error: $error');
          }
        });
  }

  /// Send a simple text message (convenience method)
  Future<Stream<Map<String, dynamic>>> sendTextMessage(
    String message, {
    String? sessionId,
  }) {
    return sendMessage(
      message: message,
      sessionId: sessionId,
    );
  }

  /// Send a message with images (convenience method)
  Future<Stream<Map<String, dynamic>>> sendMessageWithImages(
    String message,
    List<File> images, {
    String? sessionId,
  }) {
    return sendMessage(
      message: message,
      sessionId: sessionId,
      images: images,
    );
  }
}
