import 'dart:convert';
import 'package:http/http.dart' as http;
import 'package:best_flutter_ui_templates/constants.dart';
import 'package:shared_preferences/shared_preferences.dart';

class ChatService {
  Future<Stream<Map<String, dynamic>>> sendMessage({
    required String message,
    String? sessionId,
    List<http.MultipartFile>? imageFiles,
  }) async {
    final prefs = await SharedPreferences.getInstance();
    final token = prefs.getString(Constants.authTokenKey);

    if (token == null) {
      throw Exception('Authentication token not found');
    }

    final uri = Uri.parse('${Constants.apiBaseUrl}/agent/chat');
    final request = http.MultipartRequest('POST', uri);

    request.headers['Authorization'] = 'Bearer $token';
    request.fields['message'] = message;
    if (sessionId != null) {
      request.fields['session_id'] = sessionId;
    }
    if (imageFiles != null) {
      request.files.addAll(imageFiles);
    }

    final response = await request.send();

    if (response.statusCode == 200) {
      return response.stream
          .transform(utf8.decoder)
          .transform(const LineSplitter())
          .where((line) => line.startsWith('data: '))
          .map((line) => jsonDecode(line.substring(6)));
    } else {
      throw Exception('Failed to send message: ${response.statusCode}');
    }
  }
}
