import 'package:flutter/material.dart';

class ToolCallWidget extends StatefulWidget {
  final String toolName;
  final Map<String, dynamic> arguments;
  final String? output;

  const ToolCallWidget({
    super.key,
    required this.toolName,
    required this.arguments,
    this.output,
  });

  @override
  State<ToolCallWidget> createState() => _ToolCallWidgetState();
}

class _ToolCallWidgetState extends State<ToolCallWidget> {
  bool _showOutput = false;

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: const EdgeInsets.symmetric(vertical: 8.0),
      padding: const EdgeInsets.all(12.0),
      decoration: BoxDecoration(
        color: Colors.grey[800],
        borderRadius: BorderRadius.circular(8.0),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              const Icon(Icons.build, color: Colors.white, size: 16.0),
              const SizedBox(width: 8.0),
              Text(
                'Tool: ${widget.toolName}',
                style: const TextStyle(color: Colors.white, fontWeight: FontWeight.bold),
              ),
            ],
          ),
          const SizedBox(height: 8.0),
          Text(
            'Arguments: ${widget.arguments}',
            style: const TextStyle(color: Colors.white),
          ),
          if (widget.output != null)
            Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                const SizedBox(height: 8.0),
                const Divider(color: Colors.white54),
                const SizedBox(height: 8.0),
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    const Text(
                      'Output:',
                      style: TextStyle(color: Colors.white, fontWeight: FontWeight.bold),
                    ),
                    IconButton(
                      icon: Icon(_showOutput ? Icons.keyboard_arrow_up : Icons.keyboard_arrow_down),
                      onPressed: () {
                        setState(() {
                          _showOutput = !_showOutput;
                        });
                      },
                    ),
                  ],
                ),
                if (_showOutput)
                  Text(
                    widget.output!,
                    style: const TextStyle(color: Colors.white),
                  ),
              ],
            )
          else
            const Padding(
              padding: EdgeInsets.only(top: 8.0),
              child: CircularProgressIndicator(),
            ),
        ],
      ),
    );
  }
}
