import 'package:flutter/material.dart';
import 'package:best_flutter_ui_templates/components/pulsating_dot_indicator.dart';

class TypingIndicator extends StatelessWidget {
  const TypingIndicator({super.key});

  @override
  Widget build(BuildContext context) {
    return const Padding(
      padding: EdgeInsets.symmetric(vertical: 16.0),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          PulsatingDotIndicator(),
        ],
      ),
    );
  }
}
