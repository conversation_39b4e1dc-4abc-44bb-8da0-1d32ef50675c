import 'dart:async';
import 'dart:convert';
import 'dart:io';
import 'dart:typed_data';
import 'package:dio/dio.dart';
import 'package:flutter/foundation.dart';
import 'package:best_flutter_ui_templates/services/api_client_service.dart';

/// Event types for streaming chat responses
enum ChatEventType {
  sessionId,
  textChunk,
  toolStart,
  toolEnd,
  error,
  done,
}

/// Represents a streaming chat event
class ChatEvent {
  final ChatEventType type;
  final dynamic data;

  const ChatEvent({
    required this.type,
    required this.data,
  });

  factory ChatEvent.fromJson(Map<String, dynamic> json) {
    final typeString = json['type'] as String;
    final data = json['data'];

    ChatEventType type;
    switch (typeString) {
      case 'session_id':
        type = ChatEventType.sessionId;
        break;
      case 'text_chunk':
        type = ChatEventType.textChunk;
        break;
      case 'tool_start':
        type = ChatEventType.toolStart;
        break;
      case 'tool_end':
        type = ChatEventType.toolEnd;
        break;
      case 'error':
        type = ChatEventType.error;
        break;
      default:
        type = ChatEventType.error;
        break;
    }

    return ChatEvent(type: type, data: data);
  }

  @override
  String toString() => 'ChatEvent(type: $type, data: $data)';
}

/// Enhanced streaming chat service with robust error handling and reconnection
class StreamingChatService {
  static StreamingChatService? _instance;
  static StreamingChatService get instance => _instance ??= StreamingChatService._();
  
  StreamingChatService._();

  /// Send a message and get a stream of chat events
  Future<Stream<ChatEvent>> sendMessage({
    required String message,
    String? sessionId,
    List<File>? images,
  }) async {
    try {
      final apiService = ApiClientService.instance;
      
      // Prepare form data
      final formData = FormData();
      formData.fields.add(MapEntry('message', message));
      
      if (sessionId != null) {
        formData.fields.add(MapEntry('session_id', sessionId));
      }

      // Add images if provided
      if (images != null && images.isNotEmpty) {
        for (final image in images) {
          final bytes = await image.readAsBytes();
          final multipartFile = MultipartFile.fromBytes(
            bytes,
            filename: image.path.split('/').last,
          );
          formData.files.add(MapEntry('images', multipartFile));
        }
      }

      // Make streaming request
      final response = await apiService.dio.post<ResponseBody>(
        '/api/v1/agent/chat',
        data: formData,
        options: Options(
          responseType: ResponseType.stream,
          headers: {
            'Accept': 'text/event-stream',
            'Cache-Control': 'no-cache',
            'Connection': 'keep-alive',
          },
        ),
      );

      if (response.statusCode == 200 && response.data != null) {
        return _parseSSEStream(response.data!.stream);
      } else {
        throw Exception('Failed to send message: ${response.statusCode}');
      }
    } catch (e) {
      if (kDebugMode) {
        print('Error in sendMessage: $e');
      }
      // Return an error stream
      return Stream.fromIterable([
        ChatEvent(
          type: ChatEventType.error,
          data: 'Failed to send message: $e',
        ),
      ]);
    }
  }

  /// Parse Server-Sent Events stream into ChatEvent objects
  Stream<ChatEvent> _parseSSEStream(Stream<Uint8List> stream) {
    final controller = StreamController<ChatEvent>();
    late StreamSubscription subscription;

    subscription = stream
        .transform(utf8.decoder)
        .transform(const LineSplitter())
        .where((line) => line.trim().isNotEmpty)
        .where((line) => line.startsWith('data: '))
        .listen(
          (line) {
            try {
              final jsonString = line.substring(6); // Remove 'data: ' prefix
              
              // Handle special case for connection close
              if (jsonString.trim() == '[DONE]') {
                controller.add(const ChatEvent(type: ChatEventType.done, data: null));
                controller.close();
                return;
              }

              final json = jsonDecode(jsonString) as Map<String, dynamic>;
              final event = ChatEvent.fromJson(json);
              controller.add(event);
            } catch (e) {
              if (kDebugMode) {
                print('Error parsing SSE line: $line, error: $e');
              }
              controller.add(ChatEvent(
                type: ChatEventType.error,
                data: 'Failed to parse server response: $e',
              ));
            }
          },
          onError: (error) {
            if (kDebugMode) {
              print('Stream error: $error');
            }
            controller.add(ChatEvent(
              type: ChatEventType.error,
              data: 'Stream error: $error',
            ));
            controller.close();
          },
          onDone: () {
            controller.add(const ChatEvent(type: ChatEventType.done, data: null));
            controller.close();
          },
          cancelOnError: false,
        );

    // Clean up subscription when controller is closed
    controller.onCancel = () {
      subscription.cancel();
    };

    return controller.stream;
  }

  /// Send a simple text message (convenience method)
  Future<Stream<ChatEvent>> sendTextMessage(
    String message, {
    String? sessionId,
  }) {
    return sendMessage(
      message: message,
      sessionId: sessionId,
    );
  }

  /// Send a message with images (convenience method)
  Future<Stream<ChatEvent>> sendMessageWithImages(
    String message,
    List<File> images, {
    String? sessionId,
  }) {
    return sendMessage(
      message: message,
      sessionId: sessionId,
      images: images,
    );
  }
}
