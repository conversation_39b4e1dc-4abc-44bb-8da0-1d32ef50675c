import 'package:openapi/openapi.dart';
import 'package:best_flutter_ui_templates/constants.dart';
import 'package:dio/dio.dart';
import 'package:flutter/material.dart';
import 'package:shared_preferences/shared_preferences.dart';

class ConversationDrawer extends StatefulWidget {
  final Function(String) onConversationSelected;

  const ConversationDrawer({super.key, required this.onConversationSelected});

  @override
  State<ConversationDrawer> createState() => _ConversationDrawerState();
}

class _ConversationDrawerState extends State<ConversationDrawer> {
  late Future<List<String>> _conversations;
  late final Openapi openapi;

  @override
  void initState() {
    super.initState();
    _conversations = _initializeAndFetchConversations();
  }

  Future<List<String>> _initializeAndFetchConversations() async {
    await _initializeApi();
    return _fetchConversations();
  }

  Future<void> _initializeApi() async {
    final token = await _getToken();
    if (token == null) {
      throw Exception('Authentication token not found. Please log in again.');
    }
    final dio = Dio();
    dio.options.headers['Authorization'] = 'Bearer $token';
    dio.options.baseUrl = Constants.apiBaseUrl;
    openapi = Openapi(dio: dio);
  }

  Future<String?> _getToken() async {
    final prefs = await SharedPreferences.getInstance();
    return prefs.getString(Constants.authTokenKey);
  }

  Future<List<String>> _fetchConversations() async {
    try {
      final response = await openapi.getAgentApi().agentGetConversations();
      if (response.statusCode == 200 && response.data != null) {
        return List<String>.from(response.data!);
      }
    } catch (e) {
      print('Error fetching conversations: $e');
    }
    return [];
  }

  @override
  Widget build(BuildContext context) {
    return Drawer(
      child: FutureBuilder<List<String>>(
        future: _conversations,
        builder: (context, snapshot) {
          if (snapshot.connectionState == ConnectionState.waiting) {
            return const Center(child: CircularProgressIndicator());
          } else if (snapshot.hasError) {
            return const Center(child: Text('Error loading conversations'));
          } else if (!snapshot.hasData || snapshot.data!.isEmpty) {
            return const Center(child: Text('No conversations yet'));
          }

          final conversations = snapshot.data!;
          return ListView.builder(
            itemCount: conversations.length,
            itemBuilder: (context, index) {
              final conversationId = conversations[index];
              return ListTile(
                title: Text('Conversation $conversationId'),
                onTap: () {
                  widget.onConversationSelected(conversationId);
                  Navigator.pop(context);
                },
              );
            },
          );
        },
      ),
    );
  }
}

