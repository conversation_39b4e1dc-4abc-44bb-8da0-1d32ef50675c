import 'dart:ui';
import 'dart:io';

import 'package:best_flutter_ui_templates/yourai/yourai_theme.dart';
import 'package:flutter/material.dart';
import 'package:image_picker/image_picker.dart';

class ChatInputField extends StatefulWidget {
  const ChatInputField({
    super.key,
    required this.onSendMessage,
    required this.onSendMessageWithImages,
  });

  final Function(String) onSendMessage;
  final Function(String, List<File>) onSendMessageWithImages;

  @override
  State<ChatInputField> createState() => _ChatInputFieldState();
}

class _ImageAttachment {
  final File file;
  final AnimationController controller;

  _ImageAttachment({
    required this.file,
    required this.controller,
  });
}

class _ChatInputFieldState extends State<ChatInputField>
    with TickerProviderStateMixin {
  final TextEditingController _textController = TextEditingController();
  final ImagePicker _picker = ImagePicker();
  bool _isComposing = false;
  final List<_ImageAttachment> _selectedImages = [];

  @override
  void dispose() {
    for (var attachment in _selectedImages) {
      attachment.controller.dispose();
    }
    _textController.dispose();
    super.dispose();
  }

  void _handleSubmitted(String text) {
    _textController.clear();
    setState(() {
      _isComposing = false;
    });

    if (_selectedImages.isNotEmpty) {
      final imageFiles = _selectedImages.map((e) => e.file).toList();
      widget.onSendMessageWithImages(text, imageFiles);
      setState(() {
        for (var attachment in _selectedImages) {
          attachment.controller.dispose();
        }
        _selectedImages.clear();
      });
    } else {
      widget.onSendMessage(text);
    }
  }

  Future<void> _pickImage() async {
    try {
      final XFile? image = await _picker.pickImage(
        source: ImageSource.gallery,
        maxHeight: 1024,
        maxWidth: 1024,
        imageQuality: 85,
      );

      if (image != null) {
        final animationController = AnimationController(
          vsync: this,
          duration: const Duration(milliseconds: 300),
        );
        final attachment = _ImageAttachment(
          file: File(image.path),
          controller: animationController,
        );
        setState(() {
          _selectedImages.add(attachment);
          _isComposing = true;
        });
        animationController.forward();
      }
    } catch (e) {
      print('Error picking image: $e');
    }
  }

  Future<void> _takePhoto() async {
    try {
      final XFile? image = await _picker.pickImage(
        source: ImageSource.camera,
        maxHeight: 1024,
        maxWidth: 1024,
        imageQuality: 85,
      );

      if (image != null) {
        final animationController = AnimationController(
          vsync: this,
          duration: const Duration(milliseconds: 300),
        );
        final attachment = _ImageAttachment(
          file: File(image.path),
          controller: animationController,
        );
        setState(() {
          _selectedImages.add(attachment);
          _isComposing = true;
        });
        animationController.forward();
      }
    } catch (e) {
      print('Error taking photo: $e');
    }
  }

  void _removeImage(int index) {
    final attachment = _selectedImages[index];
    attachment.controller.dispose();
    setState(() {
      _selectedImages.removeAt(index);
      _isComposing = _textController.text.isNotEmpty || _selectedImages.isNotEmpty;
    });
  }

  void _showImageSourceActionSheet() {
    showModalBottomSheet(
      context: context,
      backgroundColor: Colors.transparent,
      builder: (BuildContext context) {
        return ClipRRect(
          borderRadius: const BorderRadius.vertical(top: Radius.circular(20)),
          child: BackdropFilter(
            filter: ImageFilter.blur(sigmaX: 20, sigmaY: 20),
            child: Container(
              decoration: BoxDecoration(
                borderRadius: const BorderRadius.vertical(top: Radius.circular(20)),
                gradient: LinearGradient(
                  begin: Alignment.topLeft,
                  end: Alignment.bottomRight,
                  colors: [
                    Colors.white.withOpacity(0.2),
                    Colors.white.withOpacity(0.1),
                  ],
                ),
                border: Border.all(
                  color: Colors.white.withOpacity(0.2),
                  width: 1.5,
                ),
              ),
              child: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  ListTile(
                    leading: const Icon(Icons.photo_library, color: Colors.white),
                    title: const Text('Gallery', style: TextStyle(color: Colors.white)),
                    onTap: () {
                      Navigator.pop(context);
                      _pickImage();
                    },
                  ),
                  ListTile(
                    leading: const Icon(Icons.camera_alt, color: Colors.white),
                    title: const Text('Camera', style: TextStyle(color: Colors.white)),
                    onTap: () {
                      Navigator.pop(context);
                      _takePhoto();
                    },
                  ),
                  const SizedBox(height: 20),
                ],
              ),
            ),
          ),
        );
      },
    );
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        // Image preview row
        if (_selectedImages.isNotEmpty)
          AnimatedContainer(
            duration: const Duration(milliseconds: 300),
            margin: const EdgeInsets.only(left: 16, right: 16, bottom: 8),
            height: 80,
            child: ListView.builder(
              scrollDirection: Axis.horizontal,
              itemCount: _selectedImages.length,
              itemBuilder: (context, index) {
                final attachment = _selectedImages[index];
                return ScaleTransition(
                  scale: attachment.controller,
                  child: Container(
                    margin: const EdgeInsets.only(right: 8),
                    child: Stack(
                      children: [
                        ClipRRect(
                          borderRadius: BorderRadius.circular(8),
                          child: Image.file(
                            attachment.file,
                            width: 80,
                            height: 80,
                            fit: BoxFit.cover,
                          ),
                        ),
                        Positioned(
                          top: 4,
                          right: 4,
                          child: GestureDetector(
                            onTap: () => _removeImage(index),
                            child: Container(
                              padding: const EdgeInsets.all(2),
                              decoration: const BoxDecoration(
                                color: Colors.red,
                                shape: BoxShape.circle,
                              ),
                              child: const Icon(
                                Icons.close,
                                size: 16,
                                color: Colors.white,
                              ),
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),
                );
              },
            ),
          ),
        // Input field
        ClipRRect(
          borderRadius: BorderRadius.circular(28),
          child: BackdropFilter(
            filter: ImageFilter.blur(sigmaX: 20, sigmaY: 20),
            child: Container(
              margin: const EdgeInsets.only(left: 16, right: 16, bottom: 24),
              padding:
                  const EdgeInsets.symmetric(horizontal: 16.0, vertical: 8.0),
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(28),
                gradient: LinearGradient(
                  begin: Alignment.topLeft,
                  end: Alignment.bottomRight,
                  colors: [
                    Colors.white.withOpacity(0.2),
                    Colors.white.withOpacity(0.1),
                  ],
                ),
                border: Border.all(
                  color: Colors.white.withOpacity(0.2),
                  width: 1.5,
                ),
              ),
              child: IconTheme(
                data: const IconThemeData(color: Colors.white),
                child: Row(
                  children: <Widget>[
                    IconButton(
                      icon: const Icon(Icons.image),
                      onPressed: _showImageSourceActionSheet,
                    ),
                    Expanded(
                      child: TextField(
                        controller: _textController,
                        onChanged: (String text) {
                          setState(() {
                            _isComposing = text.isNotEmpty || _selectedImages.isNotEmpty;
                          });
                        },
                        onSubmitted: _isComposing ? _handleSubmitted : null,
                        decoration: const InputDecoration.collapsed(
                          hintText: 'Send a message',
                          hintStyle: TextStyle(color: Colors.white70),
                        ),
                        style: const TextStyle(color: Colors.white),
                      ),
                    ),
                    IconButton(
                      icon: const Icon(Icons.send),
                      onPressed: _isComposing
                          ? () => _handleSubmitted(_textController.text)
                          : null,
                    ),
                  ],
                ),
              ),
            ),
          ),
        ),
      ],
    );
  }
}
