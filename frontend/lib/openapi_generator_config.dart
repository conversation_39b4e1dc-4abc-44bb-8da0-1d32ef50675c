// Openapi Generator last run: : 2025-07-06T13:54:11.496531
// OpenAPI Generator Configuration
// This file configures the openapi_generator package for generating
// a Dart API client from the OpenAPI specification.

import 'package:openapi_generator_annotations/openapi_generator_annotations.dart';

@Openapi(
  additionalProperties: DioProperties(
    pubName: 'api_client',
    pubAuthor: 'Best Flutter UI Templates',
    pubDescription: 'Generated API client for YourAI backend services',
    pubVersion: '1.0.0',
    // Enable null safety and modern Dart features
    nullableFields: true,
    // Use proper enum handling
    enumUnknownDefaultCase: true,
  ),
  inputSpec: RemoteSpec(
    path: 'http://localhost:8000/api/v1/openapi.json',
  ),
  generatorName: Generator.dio,
  runSourceGenOnOutput: true,
  outputDirectory: 'api_client',
  // Skip files that might conflict with custom implementations
  skipSpecValidation: false,
  // Additional generator options
  typeMappings: {
    'DateTime': 'DateTime',
  },
  importMappings: {},
  // Reserve model names to avoid conflicts
  reservedWordsMappings: {},
)
class OpenApiGeneratorConfig {}