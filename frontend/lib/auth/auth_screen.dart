import 'package:best_flutter_ui_templates/yourai/yourai_home_screen.dart';
import 'package:best_flutter_ui_templates/utils/login_status.dart';
import 'package:flutter/material.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import 'package:provider/provider.dart';
import 'package:best_flutter_ui_templates/yourai/yourai_theme.dart';
import 'package:best_flutter_ui_templates/services/auth_service.dart';
import 'package:awesome_snackbar_content/awesome_snackbar_content.dart';
import 'package:openapi/api.dart';

class AuthScreen extends StatefulWidget {
  const AuthScreen({super.key});
  @override
  State<AuthScreen> createState() => _AuthScreenState();
}

class _AuthScreenState extends State<AuthScreen> {
  late final AuthService _authService;
  bool _isSigningIn = false;
  final _formKey = GlobalKey<FormState>();
  final _usernameController = TextEditingController();
  final _passwordController = TextEditingController();
  bool _isPasswordVisible = false;

  @override
  void initState() {
    super.initState();
    _authService = AuthService.instance;
    _initializeAuthService();
  }

  Future<void> _initializeAuthService() async {
    try {
      await _authService.initialize();
    } catch (e) {
      _showErrorSnackBar('Failed to initialize authentication service');
    }
  }

  Future<void> _loginWithPassword() async {
    if (_formKey.currentState!.validate()) {
      setState(() {
        _isSigningIn = true;
      });

      try {
        final user = await _authService.loginWithEmailPassword(
          _usernameController.text,
          _passwordController.text,
        );
        _handleLoginSuccess(user);
      } on AuthException catch (e) {
        _showErrorSnackBar(e.message);
      } catch (e) {
        _showErrorSnackBar('Login failed: ${e.toString()}');
      } finally {
        setState(() {
          _isSigningIn = false;
        });
      }
    }
  }

  Future<void> _loginWithGoogle() async {
    setState(() {
      _isSigningIn = true;
    });

    try {
      final user = await _authService.loginWithGoogle();
      _handleLoginSuccess(user);
    } on AuthException catch (e) {
      _showErrorSnackBar(e.message);
    } catch (e) {
      _showErrorSnackBar('Google login failed: ${e.toString()}');
    } finally {
      setState(() {
        _isSigningIn = false;
      });
    }
  }

  void _handleLoginSuccess(UserPublic user) async {
    // Update login status
    Provider.of<LoginStatus>(context, listen: false).login();

    // Show success message
    _showSuccessSnackBar('Welcome back, ${user.fullName ?? user.email}!');

    // Navigate to home screen
    Navigator.pushReplacement(
      context,
      MaterialPageRoute(
        builder: (context) => YourAIHomeScreen(),
      ),
    );
  }

  void _showSuccessSnackBar(String message) {
    final snackBar = SnackBar(
      elevation: 0,
      behavior: SnackBarBehavior.floating,
      backgroundColor: Colors.transparent,
      content: AwesomeSnackbarContent(
        title: 'Success!',
        message: message,
        contentType: ContentType.success,
      ),
    );
    ScaffoldMessenger.of(context).showSnackBar(snackBar);
  }

  void _showErrorSnackBar(String message) {
    final snackBar = SnackBar(
      elevation: 0,
      behavior: SnackBarBehavior.floating,
      backgroundColor: Colors.transparent,
      content: AwesomeSnackbarContent(
        title: 'Error!',
        message: message,
        contentType: ContentType.failure,
      ),
    );
    ScaffoldMessenger.of(context).showSnackBar(snackBar);
  }

  void _loginWithoutGoogle() {
    _usernameController.text = '<EMAIL>';
    _passwordController.text = 'Ph7eeesM.m7WteT4Cy-M';
    _loginWithPassword();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: YourAITheme.background,
      body: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Form(
          key: _formKey,
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            crossAxisAlignment: CrossAxisAlignment.stretch,
            children: [
              Text(
                'YourAI',
                textAlign: TextAlign.center,
                style: TextStyle(
                  fontFamily: YourAITheme.fontName,
                  fontSize: 48,
                  fontWeight: FontWeight.bold,
                  color: YourAITheme.darkerText,
                ),
              ),
              const SizedBox(height: 8),
              Text(
                'Welcome back!',
                textAlign: TextAlign.center,
                style: TextStyle(
                  fontFamily: YourAITheme.fontName,
                  fontSize: 18,
                  color: YourAITheme.darkText,
                ),
              ),
              const SizedBox(height: 48),
              _usernameField(),
              const SizedBox(height: 16),
              _passwordField(),
              const SizedBox(height: 24),
              _isSigningIn
                  ? const Center(child: CircularProgressIndicator())
                  : _loginButton(),
              const SizedBox(height: 16),
              _googleLoginButton(),
              const SizedBox(height: 16),
              _loginWithoutGoogleButton(),
            ],
          ),
        ),
      ),
    );
  }

  Widget _usernameField() {
    return TextFormField(
      style: TextStyle(
        fontFamily: YourAITheme.fontName,
        color: YourAITheme.darkerText,
      ),
      controller: _usernameController,
      decoration: InputDecoration(
        labelText: 'Username or Email',
        labelStyle: TextStyle(
          fontFamily: YourAITheme.fontName,
          color: YourAITheme.darkText,
        ),
        prefixIcon: Icon(Icons.person_outline, color: YourAITheme.darkText),
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(12),
        ),
        enabledBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(12),
          borderSide: BorderSide(color: YourAITheme.darkText),
        ),
        focusedBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(12),
          borderSide: BorderSide(color: YourAITheme.nearlyDarkBlue),
        ),
      ),
      validator: (value) {
        if (value == null || value.isEmpty) {
          return 'Please enter your username or email';
        }
        return null;
      },
    );
  }

  Widget _passwordField() {
    return TextFormField(
      style: TextStyle(
        fontFamily: YourAITheme.fontName,
        color: YourAITheme.darkerText,
      ),
      controller: _passwordController,
      obscureText: !_isPasswordVisible,
      decoration: InputDecoration(
        labelText: 'Password',
        labelStyle: TextStyle(
          fontFamily: YourAITheme.fontName,
          color: YourAITheme.darkText,
        ),
        prefixIcon: Icon(Icons.lock_outline, color: YourAITheme.darkText),
        suffixIcon: IconButton(
          icon: Icon(
            _isPasswordVisible ? Icons.visibility : Icons.visibility_off,
            color: YourAITheme.darkText,
          ),
          onPressed: () {
            setState(() {
              _isPasswordVisible = !_isPasswordVisible;
            });
          },
        ),
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(12),
        ),
        enabledBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(12),
          borderSide: BorderSide(color: YourAITheme.darkText),
        ),
        focusedBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(12),
          borderSide: BorderSide(color: YourAITheme.nearlyDarkBlue),
        ),
      ),
      validator: (value) {
        if (value == null || value.isEmpty) {
          return 'Please enter your password';
        }
        return null;
      },
    );
  }

  Widget _loginButton() {
    return SizedBox(
      height: 48,
      child: ElevatedButton(
        onPressed: _loginWithPassword,
        style: ElevatedButton.styleFrom(
          backgroundColor: YourAITheme.nearlyDarkBlue,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(12),
          ),
        ),
        child: Text(
          'Login',
          style: TextStyle(
            fontFamily: YourAITheme.fontName,
            fontSize: 16,
            fontWeight: FontWeight.w600,
            color: Colors.white,
          ),
        ),
      ),
    );
  }

  Widget _googleLoginButton() {
    return SizedBox(
      height: 48,
      child: OutlinedButton.icon(
        onPressed: _loginWithGoogle,
        icon: const FaIcon(
          FontAwesomeIcons.google,
          color: Colors.white,
          size: 20,
        ),
        label: Text(
          'Continue with Google',
          style: TextStyle(
            fontFamily: YourAITheme.fontName,
            fontSize: 16,
            color: YourAITheme.darkerText,
          ),
        ),
        style: OutlinedButton.styleFrom(
          side: BorderSide(color: YourAITheme.darkText),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(12),
          ),
        ),
      ),
    );
  }

  Widget _loginWithoutGoogleButton() {
    return TextButton(
      onPressed: _loginWithoutGoogle,
      child: Text(
        'Login with demo account',
        style: TextStyle(
          fontFamily: YourAITheme.fontName,
          color: YourAITheme.darkText,
          decoration: TextDecoration.underline,
        ),
      ),
    );
  }
}