// API Configuration
// This file contains configuration constants and environment-specific settings
// for the API client.

import 'package:flutter/foundation.dart';

/// Environment types
enum Environment {
  development,
  staging,
  production,
}

/// API configuration class that handles environment-specific settings
class ApiConfig {
  // Private constructor to prevent instantiation
  ApiConfig._();
  
  /// Current environment (can be overridden)
  static Environment _currentEnvironment = kDebugMode 
      ? Environment.development 
      : Environment.production;
  
  /// Get current environment
  static Environment get currentEnvironment => _currentEnvironment;
  
  /// Set current environment
  static void setEnvironment(Environment environment) {
    _currentEnvironment = environment;
  }
  
  /// Base URLs for different environments
  static const Map<Environment, String> _baseUrls = {
    Environment.development: 'http://localhost:8000',
    Environment.staging: 'https://staging-api.yourai.com',
    Environment.production: 'https://api.yourai.com',
  };
  
  /// Get base URL for current environment
  static String get baseUrl => _baseUrls[_currentEnvironment]!;
  
  /// Get base URL for specific environment
  static String getBaseUrl(Environment environment) => _baseUrls[environment]!;
  
  /// API endpoints
  static const String openApiEndpoint = '/api/v1/openapi.json';
  static const String authEndpoint = '/api/v1/login/access-token';
  static const String googleAuthEndpoint = '/api/v1/google/callback';
  static const String usersEndpoint = '/api/v1/users';
  static const String agentEndpoint = '/api/v1/agent';
  
  /// Timeout configurations
  static const Duration connectTimeout = Duration(seconds: 30);
  static const Duration receiveTimeout = Duration(seconds: 30);
  static const Duration sendTimeout = Duration(seconds: 30);
  
  /// Retry configurations
  static const int maxRetries = 3;
  static const Duration retryDelay = Duration(seconds: 1);
  
  /// Cache configurations
  static const Duration cacheMaxAge = Duration(minutes: 5);
  static const int maxCacheSize = 50 * 1024 * 1024; // 50MB
  
  /// Default headers
  static const Map<String, String> defaultHeaders = {
    'Content-Type': 'application/json',
    'Accept': 'application/json',
    'User-Agent': 'YourAI-Flutter-App/1.0.0',
  };
  
  /// API version
  static const String apiVersion = 'v1';
  
  /// OAuth2 configurations
  static const String tokenType = 'Bearer';
  static const String grantType = 'password';
  
  /// File upload configurations
  static const int maxFileSize = 10 * 1024 * 1024; // 10MB
  static const List<String> allowedImageTypes = [
    'image/jpeg',
    'image/png',
    'image/gif',
    'image/webp',
  ];
  
  /// Pagination defaults
  static const int defaultPageSize = 20;
  static const int maxPageSize = 100;
  
  /// Feature flags
  static const bool enableLogging = kDebugMode;
  static const bool enableCaching = true;
  static const bool enableRetry = true;
  
  /// Get full URL for an endpoint
  static String getFullUrl(String endpoint) {
    return '$baseUrl$endpoint';
  }
  
  /// Check if current environment is development
  static bool get isDevelopment => _currentEnvironment == Environment.development;
  
  /// Check if current environment is staging
  static bool get isStaging => _currentEnvironment == Environment.staging;
  
  /// Check if current environment is production
  static bool get isProduction => _currentEnvironment == Environment.production;
  
  /// Get environment-specific configuration
  static Map<String, dynamic> getEnvironmentConfig() {
    return {
      'environment': _currentEnvironment.name,
      'baseUrl': baseUrl,
      'enableLogging': enableLogging,
      'enableCaching': enableCaching,
      'enableRetry': enableRetry,
      'connectTimeout': connectTimeout.inMilliseconds,
      'receiveTimeout': receiveTimeout.inMilliseconds,
      'sendTimeout': sendTimeout.inMilliseconds,
      'maxRetries': maxRetries,
      'retryDelay': retryDelay.inMilliseconds,
      'maxFileSize': maxFileSize,
      'defaultPageSize': defaultPageSize,
      'maxPageSize': maxPageSize,
    };
  }
  
  /// Validate configuration
  static bool validateConfig() {
    try {
      // Check if base URL is valid
      final uri = Uri.parse(baseUrl);
      if (!uri.hasScheme || !uri.hasAuthority) {
        return false;
      }
      
      // Check if timeouts are reasonable
      if (connectTimeout.inSeconds <= 0 || 
          receiveTimeout.inSeconds <= 0 || 
          sendTimeout.inSeconds <= 0) {
        return false;
      }
      
      // Check if retry configuration is valid
      if (maxRetries < 0 || retryDelay.inMilliseconds < 0) {
        return false;
      }
      
      // Check if file size limits are reasonable
      if (maxFileSize <= 0) {
        return false;
      }
      
      // Check if pagination limits are valid
      if (defaultPageSize <= 0 || maxPageSize <= 0 || defaultPageSize > maxPageSize) {
        return false;
      }
      
      return true;
    } catch (e) {
      if (kDebugMode) {
        print('Configuration validation error: $e');
      }
      return false;
    }
  }
}
