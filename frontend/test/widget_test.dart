import 'package:best_flutter_ui_templates/auth/auth_screen.dart';
import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';

void main() {
  testWidgets('AuthScreen UI Test', (WidgetTester tester) async {
    // Build our app and trigger a frame.
    await tester.pumpWidget(
      const MaterialApp(
        home: AuthScreen(),
      ),
    );

    // Verify that the welcome text is rendered.
    expect(find.text('YourAI'), findsOneWidget);

    // Verify that the "Login with Google" button is rendered.
    expect(find.text('Sign in with Google'), findsOneWidget);
  });
}
