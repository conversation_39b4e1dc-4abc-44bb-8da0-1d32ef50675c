# OpenAPI Client Generation Guide

This document explains how to use the OpenAPI generator setup in this Flutter project.

## Overview

This project uses the `openapi_generator` package to automatically generate a Dart API client from the backend's OpenAPI specification. The setup follows Flutter best practices and provides a robust, type-safe way to interact with the backend API.

## Architecture

```
frontend/
├── lib/
│   ├── config/
│   │   └── api_config.dart              # API configuration and environment settings
│   ├── services/
│   │   ├── api_client_service.dart      # Centralized API client wrapper
│   │   └── auth_service.dart            # Authentication service
│   └── openapi_generator_config.dart    # OpenAPI generator configuration
├── build.yaml                          # Build runner configuration
├── api_client/                          # Generated API client (auto-generated)
└── scripts/
    └── generate-client.sh               # Client generation script
```

## Setup

### 1. Dependencies

The following dependencies are required in `pubspec.yaml`:

```yaml
dependencies:
  openapi:
    path: ./api_client
  dio: ^5.8.0+1
  openapi_generator_annotations: ^6.1.0
  shared_preferences: ^2.5.3

dev_dependencies:
  openapi_generator: ^6.1.0
  build_runner: ^2.1.10
```

### 2. Configuration Files

#### `lib/openapi_generator_config.dart`
Contains the `@Openapi` annotation that configures the generator:
- Uses `dio-next` generator for modern Dart support
- Enables null safety and proper serialization
- Configures remote spec fetching

#### `build.yaml`
Configures the build_runner to generate the client:
- Specifies generator options
- Sets up proper file targeting
- Enables source generation

#### `lib/config/api_config.dart`
Environment-specific configuration:
- Base URLs for different environments
- Timeout and retry settings
- Feature flags and constants

## Usage

### 1. Generate API Client

Run the generation script:

```bash
./scripts/generate-client.sh
```

Or use build_runner directly:

```bash
cd frontend
flutter pub run build_runner build --delete-conflicting-outputs
```

### 2. Initialize Services

In your app initialization:

```dart
import 'package:your_app/services/api_client_service.dart';
import 'package:your_app/services/auth_service.dart';
import 'package:your_app/config/api_config.dart';

void main() async {
  WidgetsFlutterBinding.ensureInitialized();
  
  // Initialize API client
  await ApiClientService.instance.initialize(
    baseUrl: ApiConfig.baseUrl,
    defaultHeaders: ApiConfig.defaultHeaders,
  );
  
  // Initialize auth service
  await AuthService.instance.initialize();
  
  runApp(MyApp());
}
```

### 3. Use in Your Code

#### Authentication
```dart
import 'package:your_app/services/auth_service.dart';

// Login with Google
try {
  final user = await AuthService.instance.loginWithGoogle();
  print('Logged in: ${user.email}');
} catch (e) {
  print('Login failed: $e');
}

// Login with email/password
try {
  final user = await AuthService.instance.loginWithEmailPassword(
    '<EMAIL>', 
    'password'
  );
  print('Logged in: ${user.email}');
} catch (e) {
  print('Login failed: $e');
}
```

#### API Calls
```dart
import 'package:your_app/services/api_client_service.dart';

// Get API client
final client = ApiClientService.instance.client;

// Make API calls
try {
  final conversations = await client.getAgentApi().agentGetConversations();
  print('Conversations: ${conversations.data}');
} catch (e) {
  print('API call failed: $e');
}
```

## Best Practices

### 1. Error Handling
- Always wrap API calls in try-catch blocks
- Use the built-in error handling in `ApiClientService`
- Provide meaningful error messages to users

### 2. Authentication
- Use `AuthService` for all authentication operations
- Token management is handled automatically
- Check authentication status before making protected API calls

### 3. Configuration
- Use `ApiConfig` for environment-specific settings
- Don't hardcode URLs or timeouts in your code
- Use feature flags for conditional functionality

### 4. Code Organization
- Keep API-related code in the `services/` directory
- Use dependency injection for better testability
- Separate business logic from API calls

## Development Workflow

### 1. Backend Changes
When the backend API changes:
1. Ensure the backend server is running
2. Run `./scripts/generate-client.sh`
3. Review generated changes
4. Update your code if needed
5. Test the changes

### 2. Environment Switching
To switch between environments:
```dart
import 'package:your_app/config/api_config.dart';

// Switch to staging
ApiConfig.setEnvironment(ApiConfig.Environment.staging);

// Reinitialize API client with new base URL
await ApiClientService.instance.initialize(
  baseUrl: ApiConfig.baseUrl,
);
```

### 3. Testing
- Use the generated models for type-safe testing
- Mock the API client service for unit tests
- Test error scenarios and edge cases

## Troubleshooting

### Common Issues

1. **Generation fails with "API server not running"**
   - Ensure your backend server is running on the correct port
   - Check the URL in `ApiConfig.baseUrl`

2. **Build errors after generation**
   - Run `flutter clean` and `flutter pub get`
   - Check for conflicting dependencies

3. **Authentication errors**
   - Verify token storage and retrieval
   - Check API endpoint configurations
   - Ensure proper error handling

4. **Type errors in generated code**
   - Update to latest `openapi_generator` version
   - Check OpenAPI spec validity
   - Review generator configuration

### Debug Mode
Enable debug logging by setting:
```dart
ApiConfig.enableLogging = true;
```

This will print detailed request/response information to the console.

## Advanced Configuration

### Custom Headers
Add custom headers globally:
```dart
ApiClientService.instance.addHeader('X-Custom-Header', 'value');
```

### Request Interceptors
The `ApiClientService` includes built-in interceptors for:
- Authentication token injection
- Request/response logging
- Error handling
- Retry logic

### Custom Error Handling
Extend the error handling by modifying `ApiClientService._handleApiError()`.

## Security Considerations

1. **Token Storage**: Tokens are stored securely using `SharedPreferences`
2. **HTTPS**: Always use HTTPS in production
3. **Token Expiration**: Implement proper token refresh logic
4. **Sensitive Data**: Never log sensitive information in production

## Performance Optimization

1. **Caching**: Implement response caching for frequently accessed data
2. **Pagination**: Use proper pagination for large datasets
3. **Connection Pooling**: Dio handles connection pooling automatically
4. **Request Debouncing**: Implement debouncing for search and filter operations
