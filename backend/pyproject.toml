[project]
name = "app"
version = "0.0.1"
description = ""
requires-python = ">=3.10,<4.0"
dependencies = [
    "fastapi[standard]<1.0.0,>=0.115.6",
    "python-multipart<1.0.0,>=0.0.7",
    "email-validator<*******,>=2.1.0.post1",
    "passlib[bcrypt]<2.0.0,>=1.7.4",
    "tenacity<9.0.0,>=8.2.3",
    "pydantic>2.0",
    "emails<1.0,>=0.6",
    "jinja2<4.0.0,>=3.1.4",
    "alembic<2.0.0,>=1.12.1",
    "httpx<1.0.0,>=0.25.1",
    "psycopg[binary]>=3.1.13,<4.0.0",
    "sqlmodel<1.0.0,>=0.0.21",
    # Pin bcrypt until passlib supports the latest
    "bcrypt==4.0.1",
    "pydantic-settings<3.0.0,>=2.2.1",
    "sentry-sdk[fastapi]<2.0.0,>=1.40.6",
    "pyjwt<3.0.0,>=2.8.0",
    "cryptography==44.0.0",
    "boto3==1.35.76",
    "redis==5.2.1",
    "python-redis-lock==4.0.0",
    "rq-scheduler==0.14.0",
    "Faker==33.1.0",
    "alembic-autogenerate-enums==0.1.2",
    "requests>=2.32.3",
    "fastapi-sso>=0.17.0",
    "authlib>=1.4.0",
    "itsdangerous>=2.2.0",
    "celery[redis]>=5.4.0",
    "flower>=2.0.1",
    "greenlet>=3.1.1",
    "psycopg-pool>=3.2.4",
    "croniter>=6.0.0",
    "loguru>=0.7.3",
    "nest-asyncio>=1.6.0",
    "minio>=7.2.15",
    "pytest-asyncio>=0.23.8",
    "python-slugify>=8.0.4",
    "miniopy-async>=1.22.1",
    "aioboto3>=13.3.0",
    "groq>=0.23.0",
    "agno[aws]>=1.7.0",
    "openai>=1.76.0",
    "psycopg2-binary>=2.9.10",
    "alembic-postgresql-enum>=1.7.0",
    "pyrefly>=0.17.1",
    "langfuse>=2.60.5",
    "slowapi>=0.1.9",
    "duckduckgo-search>=6.3.5",
    "google-genai>=1.23.0",
    "arxiv>=2.2.0",
    "pypdf>=5.7.0",
    "googlesearch-python>=1.3.0",
    "pycountry>=24.6.1",
    "tavily-python>=0.7.8",
    "rich>=14.0.0",
]

[tool.uv]
dev-dependencies = [
    "pytest<8.0.0,>=7.4.3",
    "mypy<2.0.0,>=1.8.0",
    "ruff<1.0.0,>=0.2.2",
    "pre-commit<4.0.0,>=3.6.2",
    "types-passlib<2.0.0.0,>=1.7.7.20240106",
    "coverage<8.0.0,>=7.4.3",
]

[build-system]
requires = ["hatchling"]
build-backend = "hatchling.build"

[tool.mypy]
strict = true
exclude = ["venv", ".venv", "alembic"]

[tool.ruff]
target-version = "py310"
exclude = ["alembic"]

[tool.ruff.lint]
select = [
    "E",  # pycodestyle errors
    "W",  # pycodestyle warnings
    "F",  # pyflakes
    "I",  # isort
    "B",  # flake8-bugbear
    "C4",  # flake8-comprehensions
    "UP",  # pyupgrade
    "ARG001", # unused arguments in functions
]
ignore = [
    "E501",  # line too long, handled by black
    "B008",  # do not perform function calls in argument defaults
    "W191",  # indentation contains tabs
    "B904",  # Allow raising exceptions without from e, for HTTPException
    "ARG001",  # Unused arguments in functions
]

[tool.ruff.lint.pyupgrade]
# Preserve types, even if a file imports `from __future__ import annotations`.
keep-runtime-typing = true

[tool.pytest.ini_options]
filterwarnings = [
    "ignore::DeprecationWarning",
    "ignore::UserWarning",
]
log_cli = true
log_cli_level = "INFO"
addopts = "-s"
asyncio_mode = "auto"
