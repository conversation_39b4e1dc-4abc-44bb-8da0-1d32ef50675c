import asyncio
from collections.abc import Generator

import pytest
from fastapi.testclient import Test<PERSON>lient
from sqlmodel import Session, delete
from sqlmodel.ext.asyncio.session import AsyncSession

from app.core.db import async_engine, engine, init_db
from app.main import app
from app.models import User
from app.tests.utils.user import authentication_token_from_email
from app.tests.utils.utils import get_superuser_token_headers


@pytest.fixture(scope="session", autouse=True)
def db() -> Generator[Session, None, None]:
    with Session(engine) as session:
        # Ensure database is initialized for testing
        async def init_test_db():
            async with AsyncSession(async_engine) as async_session:
                await init_db(async_session)

        asyncio.run(init_test_db())
        yield session

        # Clean up users after session
        statement = delete(User)
        session.execute(statement)
        session.commit()


@pytest.fixture(scope="module")
def client() -> Generator[TestClient, None, None]:
    with TestClient(app) as c:
        yield c


@pytest.fixture(scope="function")  # Changed to function scope for better isolation
def superuser_token_headers(client: TestClient) -> dict[str, str]:
    # Ensure superuser exists for each test function
    async def ensure_superuser():
        async with AsyncSession(async_engine) as async_session:
            await init_db(async_session)

    asyncio.run(ensure_superuser())
    return get_superuser_token_headers(client)


@pytest.fixture(scope="function")  # Changed to function scope for better isolation
def normal_user_token_headers(client: TestClient) -> dict[str, str]:
    # Use a fixed test email and create user with async session
    async def create_and_authenticate():
        test_email = "<EMAIL>"
        async with AsyncSession(async_engine) as async_session:
            return await authentication_token_from_email(
                client=client, email=test_email, db=async_session
            )

    return asyncio.run(create_and_authenticate())
