import asyncio
import json
import logging
from collections.abc import Callable
from typing import Any

from redis.exceptions import RedisError

from app.core.redis import RedisPubSubException, queue_processor_running, redis_conn
from app.core.redis.const import AppQueue

logger = logging.getLogger(__name__)


class RedisQueue:
    def __init__(self, queue_name: str = AppQueue.QUEUE_DEFAULT):
        self.queue_name = queue_name
        self.redis = redis_conn
        self.task_handlers: dict[str, Callable] = {}

    def enqueue(self, task_name: str, task_data: dict[str, Any]) -> bool:
        """Enqueue a task to the Redis queue."""
        try:
            task = json.dumps({"name": task_name, "data": task_data})
            self.redis.lpush(self.queue_name, task)
            return True
        except RedisError as e:
            raise RedisPubSubException(f"Failed to enqueue task: {str(e)}") from e

    async def enqueue_async(self, task_name: str, task_data: dict[str, Any]) -> bool:
        """Enqueue a task to the Redis queue asynchronously."""
        try:
            return await asyncio.to_thread(self.enqueue, task_name, task_data)
        except RedisPubSubException as e:
            raise e

    def dequeue(self) -> dict[str, Any] | None:
        """Dequeue a task from the Redis queue."""
        try:
            task = self.redis.rpop(self.queue_name)
            return json.loads(task) if task else None
        except RedisError as e:
            raise RedisPubSubException(f"Failed to dequeue task: {str(e)}") from e

    async def dequeue_async(self) -> dict[str, Any] | None:
        """Dequeue a task from the Redis queue asynchronously."""
        try:
            return await asyncio.to_thread(self.dequeue)
        except RedisPubSubException as e:
            raise e

    def register_task_handler(self, task_name: str, handler: Callable):
        """Register a handler for a specific task type."""
        self.task_handlers[task_name] = handler

    def process_task(self, task: dict[str, Any]):
        """Process a dequeued task."""
        task_name = task["name"]
        task_data = task["data"]
        handler = self.task_handlers.get(task_name)
        if handler:
            try:
                handler(task_data)
            except Exception as e:
                # Here you might want to implement retry logic or error reporting
                logger.info(f"Error processing task {task_name}: {str(e)}")
        else:
            logger.info(f"No handler registered for task {task_name}")

    async def process_tasks_async(self):
        """Continuously process tasks from the queue asynchronously."""
        while True:
            try:
                task = await self.dequeue_async()
                if task:
                    await asyncio.to_thread(self.process_task, task)
                else:
                    await asyncio.sleep(1)  # Wait a bit before checking again
            except RedisPubSubException as e:
                logger.info(f"Error processing queue: {str(e)}")
                await asyncio.sleep(5)  # Wait before retrying

    def close(self):
        """Close the Redis connection."""
        self.redis.close()


def create_queue() -> RedisQueue:
    return RedisQueue()


def task_handler_example(task_data: dict[str, Any]):
    logger.info(f"Processing task: {task_data}")


async def queue_processor_task(queue_name: str, task_name: str, handler: Callable):
    queue = RedisQueue(queue_name)

    queue.register_task_handler(task_name, handler)

    try:
        logger.info("Starting queue processor ...")
        while queue_processor_running:
            try:
                await queue.process_tasks_async()
            except RedisPubSubException as e:
                logger.info(f"Subscription error: {e}. Retrying in 5 seconds...")
                await asyncio.sleep(5)
    finally:
        logger.info("Stopping queue processor ...")
        queue.close()
