import asyncio
import logging
from collections.abc import Callable
from typing import Any

from redis.exceptions import RedisError

from app.core.redis import RedisPubSubException, redis_conn, subscriber_running

logger = logging.getLogger(__name__)


class RedisPubSub:
    def __init__(self):
        self.redis = redis_conn
        self.pubsub = self.redis.pubsub()

    def __enter__(self):
        return self

    def __exit__(self, exc_type, exc_val, exc_tb):
        self.close()

    async def __aenter__(self):
        return self

    async def __aexit__(self, exc_type, exc_val, exc_tb):
        self.close()

    def close(self):
        self.pubsub.close()
        self.redis.close()

    def publish(self, channel: str, message: Any) -> int:
        """
        Publish a message to a channel.

        :param channel: The channel to publish to
        :param message: The message to publish
        :return: The number of subscribers that received the message
        :raises RedisPubSubException: If publishing the message fails
        """
        try:
            recipients = self.redis.publish(channel, message)
            logger.info(
                f"Published message to channel {channel}. Recipients: {recipients}"
            )
            return recipients
        except RedisError as e:
            logger.error(f"Failed to publish message: {str(e)}")
            raise RedisPubSubException(f"Failed to publish message: {str(e)}") from e

    async def publish_async(self, channel: str, message: Any) -> int:
        """
        Publish a message to a channel asynchronously.

        :param channel: The channel to publish to
        :param message: The message to publish
        :return: The number of subscribers that received the message
        :raises RedisPubSubException: If publishing the message fails
        """
        try:
            recipients = await asyncio.to_thread(self.redis.publish, channel, message)
            logger.info(
                f"Published message to channel {channel}. Recipients: {recipients}"
            )
            return recipients
        except RedisError as e:
            logger.error(f"Failed to publish message: {str(e)}")
            raise RedisPubSubException(f"Failed to publish message: {str(e)}") from e

    def subscribe(self, channel: str, callback: Callable[[str, Any], None]):
        """
        Subscribe to a channel and process messages with a callback.

        :param channel: The channel to subscribe to
        :param callback: A function to call with each message
        :raises RedisPubSubException: If subscription fails
        """
        try:
            self.pubsub.subscribe(channel)
            logger.info(f"Subscribed to channel: {channel}")
            for message in self.pubsub.listen():
                if message["type"] == "message":
                    callback(message["channel"], message["data"])
        except RedisError as e:
            logger.error(f"Subscription error: {str(e)}")
            raise RedisPubSubException(f"Subscription error: {str(e)}") from e

    async def subscribe_async(self, channel: str, callback: Callable[[str, Any], None]):
        """
        Subscribe to a channel and process messages with a callback asynchronously.

        :param channel: The channel to subscribe to
        :param callback: A function to call with each message
        :raises RedisPubSubException: If subscription fails
        """
        try:
            await asyncio.to_thread(self.pubsub.subscribe, channel)
            logger.info(f"Subscribed to channel: {channel}")
            while True:
                message = await asyncio.to_thread(self.pubsub.get_message, timeout=1.0)
                if message and message["type"] == "message":
                    await asyncio.to_thread(
                        callback, message["channel"], message["data"]
                    )
        except RedisError as e:
            logger.error(f"Subscription error: {str(e)}")
            raise RedisPubSubException(f"Subscription error: {str(e)}") from e


def create_pubsub() -> RedisPubSub:
    return RedisPubSub()


async def subscriber_task(channel: str, handler: Callable):
    pubsub = create_pubsub()

    try:
        while subscriber_running:
            try:
                await pubsub.subscribe_async(channel, handler)
            except RedisPubSubException as e:
                logger.info(f"Subscription error: {e}. Retrying in 5 seconds...")
                await asyncio.sleep(5)
    finally:
        pubsub.close()
