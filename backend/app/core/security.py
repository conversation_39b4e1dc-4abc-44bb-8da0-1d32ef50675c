import uuid
from datetime import UTC, datetime, timedelta
from typing import Any

import jwt
from passlib.context import Crypt<PERSON>ontext

from app.core.config import settings

pwd_context = CryptContext(schemes=["bcrypt"], deprecated="auto")


ALGORITHM = "HS256"
ALLOWED_ALGORITHMS = ["HS256"]  # Explicitly define allowed algorithms


def create_access_token(
    subject: uuid.UUID | str | Any, expires_delta: timedelta
) -> str:
    """Create a JWT access token with proper UUID handling"""
    expire = datetime.now(UTC) + expires_delta

    # Ensure subject is properly converted to string
    if isinstance(subject, uuid.UUID):
        subject_str = str(subject)
    else:
        subject_str = str(subject)

    to_encode = {"exp": expire, "sub": subject_str}
    encoded_jwt = jwt.encode(to_encode, settings.SECRET_KEY, algorithm=ALGORITHM)
    return encoded_jwt


def verify_password(plain_password: str, hashed_password: str) -> bool:
    return pwd_context.verify(plain_password, hashed_password)


def get_password_hash(password: str) -> str:
    return pwd_context.hash(password)
