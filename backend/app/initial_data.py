import asyncio
import logging

from app.api.deps import get_async_session
from app.core.db import init_db

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


async def init() -> None:
    session_gen = get_async_session()
    session = await session_gen.__anext__()
    await init_db(session)


def main() -> None:
    logger.info("Creating initial data")
    asyncio.run(init())
    logger.info("Initial data created")


if __name__ == "__main__":
    main()
