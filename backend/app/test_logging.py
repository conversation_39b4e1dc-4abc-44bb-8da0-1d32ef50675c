#!/usr/bin/env python3
"""
Test script to verify loguru logging configuration is working properly.
This script demonstrates:
1. Normal log messages with colors
2. Exception handling with full stack traces
3. Integration with uvicorn/FastAPI logging
"""

import asyncio
import sys
import traceback
from app.logger import logger


def test_basic_logging():
    """Test basic logging functionality."""
    logger.info("🚀 Testing basic logging functionality")
    logger.debug("Debug message - should appear with colors")
    logger.info("Info message - should appear with colors")
    logger.warning("Warning message - should appear with colors")
    logger.error("Error message - should appear with colors")
    logger.success("Success message - should appear with colors")


def test_exception_logging():
    """Test exception logging with full stack traces."""
    logger.info("🧪 Testing exception logging with stack traces")

    def level_3():
        """Third level function that will raise an exception."""
        x = 1
        y = 0
        return x / y  # This will raise ZeroDivisionError

    def level_2():
        """Second level function."""
        data = {"test": "value"}
        return level_3() + len(data)

    def level_1():
        """First level function."""
        items = [1, 2, 3]
        return level_2() * len(items)

    try:
        result = level_1()
    except ZeroDivisionError:
        # This should show full stack trace with variable values
        logger.opt(exception=True).error("ZeroDivisionError occurred with full context")


def test_manual_exception():
    """Test manual exception logging."""
    logger.info("🔍 Testing manual exception creation")

    try:
        # Create a more complex exception scenario
        def process_data(data):
            if not data:
                raise ValueError("Data cannot be empty")
            if "required_field" not in data:
                raise KeyError("Missing required_field in data")
            return data["required_field"].upper()

        # Test with invalid data
        invalid_data = {"optional_field": "test"}
        result = process_data(invalid_data)

    except (ValueError, KeyError) as e:
        logger.opt(exception=True).error("Data processing failed")


def test_async_logging():
    """Test logging in async context."""
    async def async_function():
        logger.info("🔄 Testing async logging")
        await asyncio.sleep(0.1)
        logger.info("Async operation completed")

        try:
            await asyncio.sleep(0.1)
            raise RuntimeError("Async error for testing")
        except RuntimeError:
            logger.opt(exception=True).error("Async exception occurred")

    return asyncio.run(async_function())


def test_structured_logging():
    """Test structured logging with context."""
    logger.info("📊 Testing structured logging")

    # Test with bound context
    request_logger = logger.bind(
        request_id="req-12345",
        user_id="user-67890",
        endpoint="/api/test"
    )

    request_logger.info("Processing request")
    request_logger.warning("Request took longer than expected")

    try:
        # Simulate an error
        raise ConnectionError("Database connection failed")
    except ConnectionError:
        request_logger.opt(exception=True).error("Request failed due to connection error")


def test_python_logging_interception():
    """Test that standard Python logging is intercepted."""
    import logging

    logger.info("🕵️ Testing Python logging interception")

    # Create standard Python logger
    py_logger = logging.getLogger("test_module")

    # These should be intercepted and formatted by loguru
    py_logger.info("Standard logging INFO message")
    py_logger.warning("Standard logging WARNING message")
    py_logger.error("Standard logging ERROR message")

    # Test with exception
    try:
        raise Exception("Test exception from standard logging")
    except Exception:
        py_logger.exception("Exception logged via standard logging")


def main():
    """Run all logging tests."""
    logger.info("🧾 Starting comprehensive logging tests")
    logger.info("=" * 60)

    test_basic_logging()
    logger.info("-" * 40)

    test_exception_logging()
    logger.info("-" * 40)

    test_manual_exception()
    logger.info("-" * 40)

    test_async_logging()
    logger.info("-" * 40)

    test_structured_logging()
    logger.info("-" * 40)

    test_python_logging_interception()
    logger.info("-" * 40)

    logger.success("✅ All logging tests completed!")
    logger.info("=" * 60)

    logger.info("🔍 Key improvements:")
    logger.info("  • Full stack traces with variable inspection")
    logger.info("  • Colored output for better readability")
    logger.info("  • Standard logging interception")
    logger.info("  • Thread-safe async logging")
    logger.info("  • Structured logging with context")


if __name__ == "__main__":
    main()
