import importlib
import logging
from pathlib import Path

from celery.signals import after_task_publish

from app.core.celery_app import celery_app
from app.schedulers.utils import get_tasks

logger = logging.getLogger(__name__)


def discover_task_modules() -> list[str]:
    """Automatically discover all task modules in the tasks directory."""
    tasks_dir = Path(__file__).parent / "tasks"
    task_modules = []

    for file in tasks_dir.glob("*.py"):
        if file.name != "__init__.py":
            module_name = f"app.tasks.{file.stem}"
            task_modules.append(module_name)

    return task_modules


def register_tasks():
    """Register all tasks from discovered modules."""
    task_modules = discover_task_modules()

    for module_name in task_modules:
        try:
            importlib.import_module(module_name)
            logger.info(f"Successfully registered tasks from {module_name}")
        except Exception as e:
            logger.error(f"Failed to register tasks from {module_name}: {str(e)}")


# Register task modules
register_tasks()


@after_task_publish.connect
def task_sent_handler(sender=None, headers=None, body=None, **kwargs):
    logger.info(f"Task sent: {sender}")
    logger.info(f"Task headers: {headers}")
    logger.info(f"Task body: {body}")
    logger.info(f"Task kwargs: {kwargs}")


celery_app.conf.beat_schedule = get_tasks()
