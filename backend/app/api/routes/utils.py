from fastapi import APIRouter, Depends
from pydantic.networks import EmailStr

from app.api.deps import UtilsServiceDep, get_current_active_superuser
from app.models import Message

router = APIRouter(prefix="/utils", tags=["utils"])


@router.post(
    "/test-email/",
    dependencies=[Depends(get_current_active_superuser)],
    status_code=201,
)
def test_email(email_to: EmailStr, utils_service: UtilsServiceDep) -> Message:
    """
    Test emails.
    """
    return utils_service.test_email(email_to=email_to)


@router.get("/health-check/")
async def health_check(utils_service: UtilsServiceDep) -> bool:
    return await utils_service.health_check()
