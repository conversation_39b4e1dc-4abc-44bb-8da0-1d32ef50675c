import json
from typing import List

from fastapi import APIRouter, Depends, File, HTTPException, UploadFile
from fastapi.responses import StreamingResponse

from app.api.deps import AgentServiceDep, CurrentUserAsync
from app.api.schemas.agent import ChatRequest, Conversation
from app.logger import logger

router = APIRouter()


@router.get("/conversations", response_model=List[str])
async def get_conversations(
    current_user: CurrentUserAsync,
    agent_service: AgentServiceDep,
) -> List[str]:
    """
    Get all conversation session IDs for the current user
    """
    try:
        logger.info(f"Getting conversations for user {current_user.id}")
        return agent_service.get_all_conversations(user_id=str(current_user.id))
    except Exception as e:
        logger.opt(exception=True).error(f"Failed to get conversations for user {current_user.id}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/conversations/{session_id}", response_model=Conversation)
async def get_conversation_history(
    session_id: str,
    current_user: CurrentUserAsync,
    agent_service: AgentServiceDep,
) -> Conversation:
    """
    Get the history of a specific conversation session
    """
    try:
        logger.info(f"Getting conversation history for session {session_id}, user {current_user.id}")
        return agent_service.get_conversation_history(
            session_id=session_id, user_id=str(current_user.id)
        )
    except Exception as e:
        logger.opt(exception=True).error(f"Failed to get conversation history for session {session_id}, user {current_user.id}")
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/chat")
async def chat_with_agent(
    current_user: CurrentUserAsync,
    agent_service: AgentServiceDep,
    chat_request: ChatRequest = Depends(ChatRequest.as_form),
    images: List[UploadFile] | None = File(None),
):
    """
    Chat with the YouRAI agent with multimodal input support (text + optional images)
    """
    try:
        logger.info(f"Chat request from user {current_user.id}, session: {chat_request.session_id}")
        response_generator = await agent_service.chat_with_agent(
            chat_request=chat_request,
            user_id=str(current_user.id),
            images=images,
        )

        async def sse_generator():
            async for chunk in response_generator:
                yield f"data: {json.dumps(chunk)}\n\n"

        return StreamingResponse(sse_generator(), media_type="text/event-stream")

    except Exception as e:
        logger.opt(exception=True).error(f"Chat failed for user {current_user.id}, session: {chat_request.session_id}")
        raise HTTPException(status_code=500, detail=str(e))
