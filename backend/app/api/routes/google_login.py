import logging

from fastapi import <PERSON><PERSON><PERSON><PERSON>, HTTPException
from fastapi.responses import RedirectResponse
from starlette.requests import Request

from app.api.deps import AuthServiceDep, SessionAsyncDep
from app.core.config import settings

logger = logging.getLogger(__name__)

router = APIRouter()


@router.get("/login")
async def google_login(auth_service: AuthServiceDep):
    """Initiate Google OAuth login flow"""
    return await auth_service.get_google_login_redirect()


@router.get("/callback")
async def google_callback(
    request: Request, session: SessionAsyncDep, auth_service: AuthServiceDep
) -> RedirectResponse:
    """Handle Google OAuth callback and login/create user"""

    try:
        result = await auth_service.handle_google_callback(request, session)

        if not result["success"]:
            return RedirectResponse(
                url=f"{settings.FRONTEND_HOST}/login?error={result['error']}&message={result['message']}"
            )

        # Build redirect URL with authentication data
        redirect_url = (
            f"{settings.FRONTEND_HOST}/google-callback"
            f"?access_token={result['access_token']}"
            f"&expires_in={result['expires_in']}"
            f"&is_first_login={str(result['is_first_login']).lower()}"
        )

        if result.get("refresh_token"):
            redirect_url += f"&refresh_token={result['refresh_token']}"

        return RedirectResponse(url=redirect_url)

    except HTTPException:
        # Handle FastAPI HTTP exceptions
        return RedirectResponse(
            url=f"{settings.FRONTEND_HOST}/login?error=oauth_error&message=OAuth authentication failed"
        )

    except Exception as e:
        # Log the actual error for debugging
        logger.error(f"Google OAuth callback error: {str(e)}", exc_info=True)
        error_message = "An unexpected error occurred during authentication"
        return RedirectResponse(
            url=f"{settings.FRONTEND_HOST}/login?error=authentication_failed&message={error_message}"
        )
