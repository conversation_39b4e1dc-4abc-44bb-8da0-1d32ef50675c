from typing import Annotated, Any

from fastapi import APIRouter, Depends, Request
from fastapi.security import OAuth2PasswordRequestForm

from app.api.deps import AuthServiceDep, CurrentUserAsync, SessionAsyncDep
from app.core.limiter import limiter
from app.models import Token, UserPublic

router = APIRouter()


@router.post("/access-token")
@limiter.limit("100/minute")
async def login_access_token(
    request: Request,
    session: SessionAsyncDep,
    auth_service: AuthServiceDep,
    form_data: Annotated[OAuth2PasswordRequestForm, Depends()],
) -> Token:
    """
    OAuth2 compatible token login, get an access token for future requests
    """
    return await auth_service.authenticate_user_login(
        session=session, email=form_data.username, password=form_data.password
    )


@router.post("/test-token", response_model=UserPublic)
async def test_token(current_user: CurrentUserAsync) -> Any:
    """
    Test access token
    """
    return current_user
