import uuid
from typing import Any

from fastapi import APIRouter, Depends, Request

from app.api.deps import (
    CurrentUserAsync,
    SessionAsyncDep,
    UserServiceDep,
    get_current_active_superuser_async,
)
from app.core.limiter import limiter
from app.models import (
    Message,
    UpdatePassword,
    UserCreate,
    UserPublic,
    UserRegister,
    UsersPublic,
    UserUpdate,
    UserUpdateMe,
)

router = APIRouter()


@router.get(
    "/",
    dependencies=[Depends(get_current_active_superuser_async)],
    response_model=UsersPublic,
)
async def read_users(
    session: SessionAsyncDep,
    user_service: UserServiceDep,
    skip: int = 0,
    limit: int = 100,
) -> Any:
    """
    Retrieve users.
    """
    return await user_service.get_users(session=session, skip=skip, limit=limit)


@router.post(
    "/",
    dependencies=[Depends(get_current_active_superuser_async)],
    response_model=UserPublic,
)
async def create_user(
    *, session: SessionAsyncDep, user_service: UserServiceDep, user_in: UserCreate
) -> Any:
    """
    Create new user.
    """
    return await user_service.create_user(session=session, user_in=user_in)


@router.patch("/me", response_model=UserPublic)
async def update_user_me(
    *,
    session: SessionAsyncDep,
    user_service: UserServiceDep,
    user_in: UserUpdateMe,
    current_user: CurrentUserAsync,
) -> Any:
    """
    Update own user.
    """
    return await user_service.update_user_profile(
        session=session, user_in=user_in, current_user=current_user
    )


@router.patch("/me/password", response_model=Message)
async def update_password_me(
    *,
    session: SessionAsyncDep,
    user_service: UserServiceDep,
    body: UpdatePassword,
    current_user: CurrentUserAsync,
) -> Any:
    """
    Update own password.
    """
    return await user_service.update_user_password(
        session=session, body=body, current_user=current_user
    )


@router.get("/me", response_model=UserPublic)
async def read_user_me(
    session: SessionAsyncDep,
    user_service: UserServiceDep,
    current_user: CurrentUserAsync,
) -> Any:
    """
    Get current user.
    """
    return await user_service.get_user_profile(
        session=session, current_user=current_user
    )


@router.delete("/me", response_model=Message)
async def delete_user_me(
    session: SessionAsyncDep,
    user_service: UserServiceDep,
    current_user: CurrentUserAsync,
) -> Any:
    """
    Delete own user.
    """
    return await user_service.delete_user_account(
        session=session, current_user=current_user
    )


@router.post("/signup", response_model=UserPublic)
@limiter.limit("100/minute")
async def register_user(
    request: Request,
    session: SessionAsyncDep,
    user_service: UserServiceDep,
    user_in: UserRegister,
) -> Any:
    """
    Create new user without the need to be logged in.
    """
    return await user_service.register_user(session=session, user_in=user_in)


@router.get("/{user_id}", response_model=UserPublic)
async def read_user_by_id(
    user_id: uuid.UUID,
    session: SessionAsyncDep,
    user_service: UserServiceDep,
    current_user: CurrentUserAsync,
) -> Any:
    """
    Get a specific user by id.
    """
    return await user_service.get_user_by_id(
        session=session, user_id=user_id, current_user=current_user
    )


@router.patch(
    "/{user_id}",
    dependencies=[Depends(get_current_active_superuser_async)],
    response_model=UserPublic,
)
async def update_user(
    *,
    session: SessionAsyncDep,
    user_service: UserServiceDep,
    user_id: uuid.UUID,
    user_in: UserUpdate,
) -> Any:
    """
    Update a user.
    """
    return await user_service.update_user_by_id(
        session=session, user_id=user_id, user_in=user_in
    )


@router.delete("/{user_id}", dependencies=[Depends(get_current_active_superuser_async)])
async def delete_user(
    session: SessionAsyncDep,
    user_service: UserServiceDep,
    current_user: CurrentUserAsync,
    user_id: uuid.UUID,
) -> Message:
    """
    Delete a user.
    """
    return await user_service.delete_user_by_id(
        session=session, current_user=current_user, user_id=user_id
    )
