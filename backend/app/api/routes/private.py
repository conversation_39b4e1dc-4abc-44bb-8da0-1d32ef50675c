from typing import Any

from fastapi import APIRouter
from pydantic import BaseModel

from app.api.deps import SessionAsyncDep, UserServiceDep
from app.models import UserPublic

router = APIRouter(tags=["private"], prefix="/private")


class PrivateUserCreate(BaseModel):
    email: str
    password: str
    full_name: str
    is_verified: bool = False


@router.post("/users/", response_model=UserPublic)
async def create_user(
    user_in: PrivateUserCreate, session: SessionAsyncDep, user_service: UserServiceDep
) -> Any:
    """
    Create a new user.
    """
    user_data = {
        "email": user_in.email,
        "password": user_in.password,
        "full_name": user_in.full_name,
    }

    return await user_service.create_private_user(session=session, user_in=user_data)
