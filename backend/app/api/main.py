from fastapi import APIRouter

from app.api.routes import agent, google_login, login, private, users, utils
from app.core.config import settings

api_router = APIRouter()

api_router.include_router(agent.router, prefix="/agent", tags=["agent"])
api_router.include_router(google_login.router, prefix="/google", tags=["google"])
api_router.include_router(login.router, prefix="/login", tags=["login"])
api_router.include_router(users.router, prefix="/users", tags=["users"])
api_router.include_router(utils.router, prefix="/utils", tags=["utils"])


if settings.ENVIRONMENT == "local":
    api_router.include_router(private.router)
