from typing import Any, List, Optional

from pydantic import BaseModel, Field


class ChatRequest(BaseModel):
    message: str = Field(..., description="The message from the user")
    session_id: Optional[str] = Field(
        None, description="The session ID for the conversation"
    )

    @classmethod
    def as_form(cls, message: str, session_id: Optional[str] = None):
        return cls(message=message, session_id=session_id)


class Conversation(BaseModel):
    session_id: str
    history: List[Any]
