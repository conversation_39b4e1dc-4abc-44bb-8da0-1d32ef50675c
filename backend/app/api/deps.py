import uuid
from collections.abc import As<PERSON><PERSON>enerator, Generator
from typing import TYPE_CHECKING, Annotated

import jwt
from fastapi import Depends, HTTPException, status
from fastapi.security import OAuth2PasswordBearer
from jwt.exceptions import InvalidTokenError
from pydantic import ValidationError
from sqlmodel import Session
from sqlmodel.ext.asyncio.session import AsyncSession

from app.core import security
from app.core.config import settings
from app.core.db import async_engine, engine
from app.models import TokenPayload, User

if TYPE_CHECKING:
    from app.services import AuthService, UserService, UtilsService
    from app.services.agent_service import AgentService


def verify_access_token(token: str) -> TokenPayload | None:
    try:
        payload = jwt.decode(
            token,
            settings.SECRET_KEY,
            algorithms=security.ALLOWED_ALGORITHMS,  # Use allowed algorithms
        )
        # Convert string UUID back to UUID object with proper validation
        if "sub" in payload and payload["sub"]:
            try:
                payload["sub"] = uuid.UUID(payload["sub"])
            except (ValueError, TypeError):
                # Log the error if needed and return None for invalid UUID
                return None
        token_data = TokenPayload(**payload)
        return token_data
    except (InvalidTokenError, ValidationError, ValueError):
        return None


reusable_oauth2 = OAuth2PasswordBearer(
    tokenUrl=f"{settings.API_V1_STR}/login/access-token"
)


def get_session() -> Generator[Session, None, None]:
    with Session(engine) as session:
        yield session


async def get_async_session() -> AsyncGenerator[AsyncSession, None]:
    async with AsyncSession(async_engine) as session:
        try:
            yield session
        finally:
            await session.close()


SessionDep = Annotated[Session, Depends(get_session)]
SessionAsyncDep = Annotated[AsyncSession, Depends(get_async_session)]
TokenDep = Annotated[str, Depends(reusable_oauth2)]


def get_current_user(session: SessionDep, token: TokenDep) -> User:
    token_data = verify_access_token(token)
    if not token_data:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Could not validate credentials",
        )

    user = session.get(User, token_data.sub)
    if not user:
        raise HTTPException(status_code=404, detail="User not found")
    if not user.is_active:
        raise HTTPException(status_code=400, detail="Inactive user")
    return user


async def get_current_user_async(session: SessionAsyncDep, token: TokenDep) -> User:
    token_data = verify_access_token(token)
    if not token_data:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Could not validate credentials",
        )

    user = await session.get(User, token_data.sub)
    if not user:
        raise HTTPException(status_code=404, detail="User not found")
    if not user.is_active:
        raise HTTPException(status_code=400, detail="Inactive user")
    return user


CurrentUser = Annotated[User, Depends(get_current_user)]
CurrentUserAsync = Annotated[User, Depends(get_current_user_async)]


def get_current_active_superuser(current_user: CurrentUser) -> User:
    if not current_user.is_superuser:
        raise HTTPException(
            status_code=403, detail="The user doesn't have enough privileges"
        )
    return current_user


async def get_current_active_superuser_async(current_user: CurrentUserAsync) -> User:
    if not current_user.is_superuser:
        raise HTTPException(
            status_code=403, detail="The user doesn't have enough privileges"
        )
    return current_user


# Service Dependencies
def get_auth_service() -> "AuthService":
    """Get AuthService instance"""
    from app.services import AuthService

    return AuthService()


def get_user_service() -> "UserService":
    """Get UserService instance"""
    from app.services import UserService

    return UserService()


def get_utils_service() -> "UtilsService":
    """Get UtilsService instance"""
    from app.services import UtilsService

    return UtilsService()


def get_agent_service() -> "AgentService":
    """Get AgentService instance"""
    from app.services.agent_service import agent_service

    return agent_service


# Service dependency type annotations
AuthServiceDep = Annotated["AuthService", Depends(get_auth_service)]
UserServiceDep = Annotated["UserService", Depends(get_user_service)]
UtilsServiceDep = Annotated["UtilsService", Depends(get_utils_service)]
AgentServiceDep = Annotated["AgentService", Depends(get_agent_service)]
