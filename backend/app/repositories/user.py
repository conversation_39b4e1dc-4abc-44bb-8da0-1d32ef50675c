from sqlmodel import Session, select
from sqlmodel.ext.asyncio.session import AsyncSession

from app.core.security import get_password_hash, verify_password
from app.models import User, UserCreate, UserUpdate


class UserRepository:
    @staticmethod
    async def create_user(
        *, session: Session | AsyncSession, user_create: UserCreate
    ) -> User:
        db_obj = User.model_validate(
            user_create,
            update={"hashed_password": get_password_hash(user_create.password)},
        )
        session.add(db_obj)

        if isinstance(session, AsyncSession):
            await session.commit()
            await session.refresh(db_obj)
        else:
            session.commit()
            session.refresh(db_obj)

        return db_obj

    @staticmethod
    async def update_user(
        *, session: Session | AsyncSession, db_user: User, user_in: UserUpdate
    ) -> User:
        user_data = user_in.model_dump(exclude_unset=True)
        extra_data = {}
        if "password" in user_data:
            password = user_data["password"]
            hashed_password = get_password_hash(password)
            extra_data["hashed_password"] = hashed_password
        db_user.sqlmodel_update(user_data, update=extra_data)
        session.add(db_user)

        if isinstance(session, AsyncSession):
            await session.commit()
            await session.refresh(db_user)
        else:
            session.commit()
            session.refresh(db_user)

        return db_user

    @staticmethod
    async def get_user_by_email(
        *, session: Session | AsyncSession, email: str
    ) -> User | None:
        statement = select(User).where(User.email == email)

        if isinstance(session, AsyncSession):
            result = await session.exec(statement)
            session_user = result.first()
        else:
            session_user = session.exec(statement).first()

        return session_user

    @staticmethod
    async def authenticate(
        *, session: Session | AsyncSession, email: str, password: str
    ) -> User | None:
        """
        Authenticate user with timing attack protection.
        Always performs password verification even if user doesn't exist.
        """
        db_user = await UserRepository.get_user_by_email(session=session, email=email)

        # Always verify password to prevent timing attacks
        if db_user:
            # User exists, verify actual password
            password_correct = verify_password(password, db_user.hashed_password)
            if password_correct and db_user.is_active:
                return db_user

        return None

    @staticmethod
    async def get_user_by_id(
        *, session: Session | AsyncSession, id: int
    ) -> User | None:
        statement = select(User).where(User.id == id)

        if isinstance(session, AsyncSession):
            result = await session.exec(statement)
            session_user = result.first()
        else:
            session_user = session.exec(statement).first()

        return session_user
