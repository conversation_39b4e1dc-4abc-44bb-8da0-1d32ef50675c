import datetime
import logging
import secrets
from typing import Any

from fastapi import <PERSON><PERSON><PERSON><PERSON>x<PERSON>
from fastapi_sso.sso.google import GoogleSSO
from starlette.requests import Request

from app.core import security
from app.core.config import settings
from app.models import Token, User, UserCreate
from app.repositories import UserRepository
from app.services.base import BaseService

logger = logging.getLogger(__name__)


class AuthService(BaseService):
    def __init__(self):
        super().__init__()
        self.google_sso = GoogleSSO(
            settings.GOOGLE_CLIENT_ID,
            settings.GOOGLE_CLIENT_SECRET,
            settings.GOOGLE_LOGIN_CALLBACK,
            allow_insecure_http=settings.ENVIRONMENT == "local",
        )

    async def get_google_login_redirect(self) -> Any:
        """Initiate Google OAuth login flow"""
        async with self.google_sso:
            return await self.google_sso.get_login_redirect()

    async def handle_google_callback(
        self, request: Request, session: Any
    ) -> dict[str, Any]:
        """Handle Google OAuth callback and authenticate user"""

        async with self.google_sso:
            google_user = await self.google_sso.verify_and_process(request)

        # Validate Google user data
        if not google_user:
            return {
                "success": False,
                "error": "invalid_user_data",
                "message": "No user data received from Google",
            }

        if not google_user.email:
            return {
                "success": False,
                "error": "email_not_provided",
                "message": "Email not provided by Google account",
            }

        # Validate email format
        if "@" not in google_user.email or "." not in google_user.email:
            return {
                "success": False,
                "error": "invalid_email",
                "message": "Invalid email format from Google",
            }

        try:
            # Look up user by email
            user = await UserRepository.get_user_by_email(
                session=session, email=google_user.email
            )

            is_first_login = False

            # Create new user if not found
            if not user:
                try:
                    user = await UserRepository.create_user(
                        session=session,
                        user_create=UserCreate(
                            email=google_user.email,
                            password=secrets.token_urlsafe(30),
                            is_active=True,
                            full_name=google_user.display_name or "Google User",
                            avatar_url=google_user.picture,
                        ),
                    )
                    is_first_login = True

                    # TODO: Send welcome email asynchronously
                    # send_welcome_email.delay(user.email, user.full_name)

                except Exception:
                    return {
                        "success": False,
                        "error": "user_creation_failed",
                        "message": "Failed to create user account",
                    }

            # Check if user account is active
            if not user.is_active:
                return {
                    "success": False,
                    "error": "account_disabled",
                    "message": "Your account has been disabled",
                }

            # Update last login time and avatar URL
            await self._update_user_login_info(session, user, google_user.picture)

            # Generate access token
            access_token = await self._generate_access_token(user.id)
            refresh_token = getattr(google_user, "refresh_token", None)

            return {
                "success": True,
                "access_token": access_token,
                "refresh_token": refresh_token,
                "expires_in": settings.ACCESS_TOKEN_EXPIRE_MINUTES * 60,
                "is_first_login": is_first_login,
            }

        except Exception as e:
            logger.error(f"Google OAuth callback error: {str(e)}", exc_info=True)
            return {
                "success": False,
                "error": "authentication_failed",
                "message": "An unexpected error occurred during authentication",
            }

    async def authenticate_user_login(
        self, session: Any, email: str, password: str
    ) -> Token:
        """Authenticate user with email and password"""
        user = await UserRepository.authenticate(
            session=session, email=email, password=password
        )

        if not user:
            raise HTTPException(status_code=400, detail="Incorrect email or password")
        elif not user.is_active:
            raise HTTPException(status_code=400, detail="Incorrect email or password")

        # Check if this is first login BEFORE updating last login time
        is_first_login = user.last_login_time is None

        # Update user's last login time
        await self._update_user_login_info(session, user)

        # Create access token
        access_token = await self._generate_access_token(user.id)

        return Token(
            access_token=access_token,
            token_type="bearer",
            expires_in=settings.ACCESS_TOKEN_EXPIRE_MINUTES * 60,
            is_first_login=is_first_login,
        )

    async def _update_user_login_info(
        self, session: Any, user: User, avatar_url: str = None
    ) -> None:
        """Update user's last login time and avatar URL"""
        user.last_login_time = datetime.datetime.now()

        if avatar_url:
            user.avatar_url = avatar_url

        try:
            session.add(user)
            await session.commit()
            await session.refresh(user)
        except Exception:
            await session.rollback()
            raise HTTPException(
                status_code=500, detail="Failed to update user information"
            )

    async def _generate_access_token(self, user_id: int) -> str:
        """Generate access token for user"""
        access_token_expires = datetime.timedelta(
            minutes=settings.ACCESS_TOKEN_EXPIRE_MINUTES
        )

        return security.create_access_token(
            subject=user_id,
            expires_delta=access_token_expires,
        )
