import uuid
from typing import Any

from fastapi import H<PERSON><PERSON><PERSON>x<PERSON>
from sqlmodel import func, select

from app.core.config import settings
from app.core.security import get_password_hash, verify_password
from app.models import (
    Message,
    UpdatePassword,
    User,
    UserCreate,
    UserPublic,
    UserRegister,
    UsersPublic,
    UserUpdate,
    UserUpdateMe,
)
from app.repositories import UserRepository
from app.services.base import BaseService
from app.utils import generate_new_account_email, send_email


class UserService(BaseService):
    async def get_users(
        self, session: Any, skip: int = 0, limit: int = 100
    ) -> UsersPublic:
        """Retrieve users with pagination"""
        count_statement = select(func.count()).select_from(User)
        count = await session.exec(count_statement)
        count = count.one()

        statement = select(User).offset(skip).limit(limit)
        result = await session.exec(statement)
        users = result.all()

        return UsersPublic(data=users, count=count)

    async def create_user(self, session: Any, user_in: UserCreate) -> UserPublic:
        """Create new user with email verification"""
        existing_user = await UserRepository.get_user_by_email(
            session=session, email=user_in.email
        )
        if existing_user:
            raise HTTPException(
                status_code=400,
                detail="The user with this email already exists in the system.",
            )

        user = await UserRepository.create_user(session=session, user_create=user_in)

        # Send welcome email if enabled
        if settings.emails_enabled and user_in.email:
            email_data = generate_new_account_email(
                email_to=user_in.email,
                username=user_in.email,
                password=user_in.password,
            )
            send_email(
                email_to=user_in.email,
                subject=email_data.subject,
                html_content=email_data.html_content,
            )

        return user

    async def register_user(self, session: Any, user_in: UserRegister) -> UserPublic:
        """Register new user without authentication"""
        existing_user = await UserRepository.get_user_by_email(
            session=session, email=user_in.email
        )
        if existing_user:
            raise HTTPException(
                status_code=400,
                detail="The user with this email already exists in the system",
            )

        user_create = UserCreate.model_validate(user_in)
        user = await UserRepository.create_user(
            session=session, user_create=user_create
        )

        return user

    async def update_user_profile(
        self, session: Any, user_in: UserUpdateMe, current_user: User
    ) -> UserPublic:
        """Update own user profile"""
        if user_in.email:
            existing_user = await UserRepository.get_user_by_email(
                session=session, email=user_in.email
            )
            if existing_user and existing_user.id != current_user.id:
                raise HTTPException(
                    status_code=409, detail="User with this email already exists"
                )

        user_data = user_in.model_dump(exclude_unset=True)
        current_user.sqlmodel_update(user_data)
        session.add(current_user)
        await session.commit()
        await session.refresh(current_user)

        return current_user

    async def update_user_password(
        self, session: Any, body: UpdatePassword, current_user: User
    ) -> Message:
        """Update user password with validation"""
        if not verify_password(body.current_password, current_user.hashed_password):
            raise HTTPException(status_code=400, detail="Incorrect password")

        if body.current_password == body.new_password:
            raise HTTPException(
                status_code=400,
                detail="New password cannot be the same as the current one",
            )

        hashed_password = get_password_hash(body.new_password)
        current_user.hashed_password = hashed_password
        session.add(current_user)
        await session.commit()

        return Message(message="Password updated successfully")

    async def get_user_profile(self, session: Any, current_user: User) -> UserPublic:
        """Get current user profile"""
        user = await session.get(User, current_user.id)
        return user

    async def delete_user_account(self, session: Any, current_user: User) -> Message:
        """Delete user account with safety checks"""
        if current_user.is_superuser:
            raise HTTPException(
                status_code=403,
                detail="Super users are not allowed to delete themselves",
            )

        await session.delete(current_user)
        await session.commit()

        return Message(message="User deleted successfully")

    async def get_user_by_id(
        self, session: Any, user_id: uuid.UUID, current_user: User
    ) -> UserPublic:
        """Get specific user by ID with permission checks"""
        user = await session.get(User, user_id)

        if user == current_user:
            return user

        if not current_user.is_superuser:
            raise HTTPException(
                status_code=403,
                detail="The user doesn't have enough privileges",
            )

        return user

    async def update_user_by_id(
        self, session: Any, user_id: uuid.UUID, user_in: UserUpdate
    ) -> UserPublic:
        """Update user by ID (admin operation)"""
        db_user = await session.get(User, user_id)
        if not db_user:
            raise HTTPException(
                status_code=404,
                detail="The user with this id does not exist in the system",
            )

        if user_in.email:
            existing_user = await UserRepository.get_user_by_email(
                session=session, email=user_in.email
            )
            if existing_user and existing_user.id != user_id:
                raise HTTPException(
                    status_code=409, detail="User with this email already exists"
                )

        db_user = await UserRepository.update_user(
            session=session, db_user=db_user, user_in=user_in
        )

        return db_user

    async def delete_user_by_id(
        self, session: Any, current_user: User, user_id: uuid.UUID
    ) -> Message:
        """Delete user by ID (admin operation)"""
        user = await session.get(User, user_id)
        if not user:
            raise HTTPException(status_code=404, detail="User not found")

        if user == current_user:
            raise HTTPException(
                status_code=403,
                detail="Super users are not allowed to delete themselves",
            )

        await session.delete(user)
        await session.commit()

        return Message(message="User deleted successfully")

    async def create_private_user(self, session: Any, user_in: dict) -> UserPublic:
        """Create user via private endpoint (development only)"""
        user = User(
            email=user_in["email"],
            full_name=user_in["full_name"],
            hashed_password=get_password_hash(user_in["password"]),
        )

        session.add(user)
        await session.commit()
        await session.refresh(user)

        return user
