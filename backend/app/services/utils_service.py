from pydantic.networks import EmailStr

from app.models import Message
from app.services.base import BaseService
from app.utils import generate_test_email, send_email


class UtilsService(BaseService):
    def test_email(self, email_to: EmailStr) -> Message:
        """Send test email"""
        email_data = generate_test_email(email_to=email_to)
        send_email(
            email_to=email_to,
            subject=email_data.subject,
            html_content=email_data.html_content,
        )
        return Message(message="Test email sent")

    async def health_check(self) -> bool:
        """Health check endpoint"""
        return True
