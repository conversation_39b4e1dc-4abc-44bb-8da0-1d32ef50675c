from typing import List

from agno.agent import Agent
from agno.media import Image
from agno.models.google import Gemini
# from agno.storage.redis import RedisStorage
# from agno.storage.sqlite import SqliteStorage
from agno.storage.postgres import PostgresStorage
from agno.tools.googlesearch import GoogleSearchTools
from fastapi import UploadFile

from app.api.schemas.agent import ChatRequest, ChatResponse, Conversation
from app.logger import logger
from app.services.base import BaseService

from app.core.db import engine

class AgentService(BaseService):
    def __init__(self):
        # self.storage = RedisStorage(
        #     prefix="agno_test",
        #     host="redis",
        #     port=6379,
        # )
        self.storage = PostgresStorage(
            table_name="agno_test",
            schema="public",
            auto_upgrade_schema=True,
            db_engine=engine
        )

        self.agent = Agent(
            name="YouRAI Assistant",
            model=Gemini(id="gemini-2.5-flash"),
            role="YouRAI Assistant",
            tools=[GoogleSearchTools()],
            storage=self.storage,
            add_history_to_messages=True,
            num_history_responses=10,
        )

    async def chat_with_agent(
        self, chat_request: ChatRequest, user_id: str, images: List[UploadFile] | None = None
    ):
        """
        Process chat request with multimodal input support
        """
        try:
            # Process uploaded images if any
            agno_images = []
            if images:
                for image_file in images:
                    if image_file.size > 0:
                        # Read image content
                        image_content = await image_file.read()
                        # Create agno Image object with raw content
                        agno_image = Image(content=image_content, detail="high")
                        agno_images.append(agno_image)

            # Run agent with multimodal input
            response = self.agent.run(
                message=chat_request.message,
                images=agno_images if agno_images else None,
                user_id=user_id,
                session_id=chat_request.session_id,
                stream=True,
            )

            return response

        except Exception as e:
            logger.error(f"Agent error: {str(e)}")
            raise Exception(f"Agent error: {str(e)}")

    def get_all_conversations(self, user_id: str) -> List[str]:
        """
        Get all conversation session IDs for a user
        """
        try:
            return self.storage.get_all_session_ids(user_id=user_id)
        except Exception as e:
            logger.error(f"Storage error getting conversations: {str(e)}")
            raise RuntimeError(f"Storage error: {str(e)}")

    def get_conversation_history(self, session_id: str, user_id: str) -> Conversation:
        """
        Get the history of a specific conversation session
        """
        try:
            agent_session = self.storage.read(session_id=session_id, user_id=user_id)

            # Extract messages from the AgentSession object
            if agent_session is None:
                return Conversation(session_id=session_id, history=[])

            messages = agent_session.memory["runs"][-1]["messages"]

            from rich import print
            print(messages)
            print(Conversation(session_id=session_id, history=messages))

            return Conversation(session_id=session_id, history=messages)
        except Exception as e:
            logger.error(f"Error getting conversation history: {str(e)}")
            raise Exception(f"Storage error: {str(e)}")


# Dependency injection - create singleton instance
agent_service = AgentService()
