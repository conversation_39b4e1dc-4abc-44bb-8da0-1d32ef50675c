import os
import sys
import logging
import inspect
from pathlib import Path
from typing import Dict, Any

from loguru import logger


class <PERSON>ceptHandler(logging.Handler):
    """Intercept standard logging messages and redirect them to loguru."""

    def emit(self, record: logging.LogRecord) -> None:
        """Emit a log record through loguru."""
        try:
            # Get corresponding loguru level
            level: str | int = logger.level(record.levelname).name
        except ValueError:
            level = record.levelno

        # Find caller from where originated the logged message
        frame, depth = inspect.currentframe(), 0
        while frame:
            filename = frame.f_code.co_filename
            # Skip logging module frames and importlib frames
            is_logging = filename == logging.__file__
            is_frozen = "importlib" in filename and "_bootstrap" in filename
            if depth > 0 and not (is_logging or is_frozen):
                break
            frame = frame.f_back
            depth += 1

        # Log with proper exception info and depth
        logger.opt(depth=depth, exception=record.exc_info).log(
            level, record.getMessage()
        )


class LoggerConfig:
    """Production-ready logger configuration with uvicorn/FastAPI integration."""

    def __init__(self):
        self.log_level = os.getenv("LOG_LEVEL", "INFO").upper()
        self.log_format = os.getenv(
            "LOG_FORMAT",
            "<green>{time:YYYY-MM-DD HH:mm:ss.SSS}</green> | "
            "<level>{level: <8}</level> | "
            "<cyan>{name}</cyan>:<cyan>{function}</cyan>:<cyan>{line}</cyan> | "
            "<level>{message}</level>"
        )
        self.log_dir = Path(os.getenv("LOG_DIR", "logs"))
        self.enable_file_logging = os.getenv("ENABLE_FILE_LOGGING", "true").lower() == "true"
        self.enable_json_logging = os.getenv("ENABLE_JSON_LOGGING", "false").lower() == "true"
        self.diagnose = os.getenv("LOG_DIAGNOSE", "true").lower() == "true"
        self.backtrace = os.getenv("LOG_BACKTRACE", "true").lower() == "true"

    def setup_logger(self) -> None:
        """Configure loguru with uvicorn/FastAPI integration."""
        # Remove default loguru handler
        logger.remove()

        # Setup intercept handler for standard logging
        self._setup_intercept_handler()

        # Configure loguru handlers
        self._setup_loguru_handlers()

        # Configure exception handling
        self._setup_exception_handling()

    def _setup_intercept_handler(self) -> None:
        """Setup interception of standard logging messages."""
        # Create intercept handler
        intercept_handler = InterceptHandler()

        # Configure root logger
        logging.root.handlers = [intercept_handler]
        logging.root.setLevel(self.log_level)

        # List of loggers to intercept
        loggers_to_intercept = [
            "uvicorn",
            "uvicorn.access",
            "uvicorn.error",
            "fastapi",
            "starlette",
            "gunicorn",
            "gunicorn.access",
            "gunicorn.error",
        ]

        # List of loggers to suppress or set to higher levels
        loggers_to_suppress = {
            "watchfiles": "WARNING",  # Reduce watchfiles noise
            "watchfiles.main": "WARNING",
            "watchfiles.watcher": "WARNING",
        }

        # Configure specific loggers and all existing ones
        seen = set()
        for name in [*logging.root.manager.loggerDict.keys(), *loggers_to_intercept]:
            if name not in seen:
                seen.add(name.split(".")[0])
                logging.getLogger(name).handlers = [intercept_handler]
                logging.getLogger(name).propagate = False

        # Configure suppressed loggers
        for logger_name, level in loggers_to_suppress.items():
            logger_instance = logging.getLogger(logger_name)
            logger_instance.setLevel(getattr(logging, level))
            logger_instance.handlers = [intercept_handler]
            logger_instance.propagate = False

    def _setup_loguru_handlers(self) -> None:
        """Setup loguru handlers for console and file output."""
        # Filter function to prevent log loops
        def filter_log_loops(record):
            message = record.get("message", "")
            # Filter out watchfiles messages about log file changes
            if "change detected" in message and ("logs/" in message or ".log" in message):
                return False
            return True

        # Console handler with colors and full diagnostics
        logger.add(
            sys.stdout,
            format=self.log_format,
            level=self.log_level,
            colorize=True,
            backtrace=self.backtrace,
            diagnose=self.diagnose,
            enqueue=True,  # Thread-safe logging
            filter=filter_log_loops,
        )

        # Error handler to stderr for critical errors
        logger.add(
            sys.stderr,
            format=self.log_format,
            level="ERROR",
            colorize=True,
            backtrace=self.backtrace,
            diagnose=self.diagnose,
            enqueue=True,
            filter=lambda record: record["level"].no >= logger.level("ERROR").no and filter_log_loops(record)
        )

        # File logging if enabled
        if self.enable_file_logging:
            self._setup_file_logging()

    def _setup_file_logging(self) -> None:
        """Setup file logging with rotation and compression."""
        # Create log directory if it doesn't exist
        self.log_dir.mkdir(exist_ok=True)

        # General application logs
        logger.add(
            self.log_dir / "app.log",
            format=self.log_format,
            level=self.log_level,
            rotation="10 MB",
            retention="30 days",
            compression="gz",
            backtrace=self.backtrace,
            diagnose=self.diagnose,
            enqueue=True,
        )

        # Error logs (separate file)
        logger.add(
            self.log_dir / "error.log",
            format=self.log_format,
            level="ERROR",
            rotation="10 MB",
            retention="90 days",
            compression="gz",
            backtrace=self.backtrace,
            diagnose=self.diagnose,
            enqueue=True,
        )

        # JSON structured logs for production parsing
        if self.enable_json_logging:
            logger.add(
                self.log_dir / "app.json",
                format=self._json_serializer,
                level=self.log_level,
                rotation="10 MB",
                retention="30 days",
                compression="gz",
                serialize=True,
                enqueue=True,
            )

    def _setup_exception_handling(self) -> None:
        """Setup enhanced exception handling."""
        # Configure exception hook for uncaught exceptions
        def exception_hook(exc_type, exc_value, exc_traceback):
            if issubclass(exc_type, KeyboardInterrupt):
                sys.__excepthook__(exc_type, exc_value, exc_traceback)
                return
            logger.opt(exception=(exc_type, exc_value, exc_traceback)).critical(
                "Uncaught exception"
            )

        sys.excepthook = exception_hook

    def _json_serializer(self, record: Dict[str, Any]) -> str:
        """Custom JSON serializer for structured logging."""
        subset = {
            "time": record["time"].isoformat(),
            "level": record["level"].name,
            "name": record["name"],
            "function": record["function"],
            "line": record["line"],
            "message": record["message"],
        }
        if record.get("exception"):
            subset["exception"] = record["exception"]
        return subset


# Initialize logger configuration
config = LoggerConfig()
config.setup_logger()

# Export configured logger
__all__ = ["logger", "config"]
